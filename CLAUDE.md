# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 建構和開發命令

### 基本命令
- `gf build -ew` - 使用 GoFrame CLI 建構專案（需先安裝 gf CLI）
- `./build.sh <version> <arch> <system>` - 使用自定義建構腳本
- `make build` - 使用 Makefile 建構
- `go run main.go` - 直接運行應用程式
- `go test ./...` - 運行所有測試

### GoFrame 相關命令
- `gf gen dao` - 生成 DAO/DO/Entity 文件
- `gf gen ctrl` - 解析 API 並生成控制器/SDK
- `gf gen service` - 生成服務文件
- `gf gen enums` - 生成枚舉文件
- `gf gen pb` - 解析 protobuf 文件並生成 go 文件

### Docker 和部署
- `make image` - 建構 Docker 鏡像
- `make image.push` - 建構並推送 Docker 鏡像
- `make deploy` - 部署到當前 kubectl 環境

## 項目架構

這是一個基於 GoFrame v2 框架的租戶管理微服務系統，使用 Weaviate 向量資料庫進行數據存儲。

### 核心組件
- **API層**: `api/tenants/v1/` - API 定義和路由
- **控制器**: `internal/controller/tenants/` - HTTP 請求處理
- **業務邏輯**: `internal/logic/` - 核心業務邏輯
  - `tenant/` - 租戶管理邏輯
  - `datastore/` - 數據存儲邏輯
  - `messageQ/` - 消息佇列處理
- **模型**: `internal/model/` - 數據模型定義
- **服務**: `internal/service/` - 服務接口實現

### 數據存儲
- **Weaviate**: 主要的向量數據庫，存儲租戶信息
- **MySQL**: 用於 LLM 參數、租戶參數等結構化數據
- **Redis**: 用於緩存和會話管理
- **RabbitMQ**: 消息佇列系統

### 配置管理
- 使用 Nacos 作為配置中心和服務註冊
- 配置文件位於 `manifest/config/config.yaml`
- 支持多環境配置（dev, test, pro）

### 關鍵常量
- 服務名稱: `tenants.svc`
- 租戶相關屬性: tenant_Id, password, app_Key, available, remark
- 敏感詞過濾: password, app_Key 等

### 安全考量
- 敏感信息（密碼、API 金鑰）需要適當處理和隱藏
- 中間件記錄請求/響應但會隱藏敏感詞
- JWT token 處理用於身份驗證

### 日誌系統
- 使用 GoFrame 內建日誌系統
- 按類別分類：debug, info, error, rr（request/response）
- 支持 MQ 專用日誌

## 開發注意事項

### 代碼生成
- 使用 GoFrame CLI 生成 DAO, Controller, Service 等代碼
- DAO/DO/Entity 文件通過 `gf gen dao` 自動生成，不要手動修改

### 測試
- 測試文件較少，新功能應該添加對應的測試
- 使用標準的 Go testing 包

### 數據庫腳本
- 數據庫表創建腳本定義在 `internal/consts/consts.go` 中
- 支持表結構變更腳本 `AlterTableFields`