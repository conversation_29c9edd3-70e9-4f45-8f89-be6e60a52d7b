package tenants

import (
	"context"
	"fmt"
	"net/http"

	"tenants/internal/model"

	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/text/gstr"

	"tenants/api/tenants/v1"
	"tenants/internal/consts"
	"tenants/internal/service"
	"tenants/utility"
)

// isAdmin checks if the current user is an admin
func (c *ControllerV1) isAdmin(ctx context.Context) bool {
	vAccount, _ := g.Cfg().Get(ctx, "system.admin_account", "admin")
	vPass, _ := g.Cfg().Get(ctx, "system.admin_pass", "a~1d@3m$")
	r := g.RequestFromCtx(ctx)

	header := r.<PERSON>eader(consts.HeaderTenantID)

	adminAccount := gstr.SplitAndTrim(header, "^")
	return adminAccount[0] == vAccount.String() && r.<PERSON>eader(consts.HeaderAppKey) == vPass.String()
}

func (c *ControllerV1) isActiveTenant(ctx context.Context) bool {
	r := g.RequestFromCtx(ctx)
	tenantID := r.GetHeader(consts.HeaderTenantID)
	accounts := gstr.SplitAndTrim(tenantID, "^")
	available := false
	if len(accounts) > 1 {

		available, _ = service.Tenant().GetTenantAvailable(ctx, accounts[1])

	} else if len(accounts) == 1 {
		available, _ = service.Tenant().GetTenantAvailable(ctx, accounts[0])

	}

	return available
}

/*
Function Name: RefreshToken
Description: Refresh an access token using the provided refresh token and return the result in a structured response.
Input Parameters:
   - ctx: Context for the request.
   - req: A RefreshTokenReq object containing the refresh token to use.
Output:
   - res: A RefreshTokenRes object containing the access token or error details.
   - err: An error if the operation encounters an issue, otherwise nil.
Exception:
   - The function invokes the 'service.RefreshToken' function to refresh the access token using the provided refresh token. If an error occurs during this process, the error is returned in the 'err' variable.
   - If the refresh token is successfully refreshed, the access token is included in the response.
   - The 'res' object includes the 'Code' field to represent the result status code, and the 'Message' field with a corresponding message.
   - The response is written to the client's response in JSON format.
*/

func (c *ControllerV1) RefreshToken(ctx context.Context, req *v1.RefreshTokenReq) (res *v1.RefreshTokenRes, err error) {
	r := g.RequestFromCtx(ctx)
	res = &v1.RefreshTokenRes{}

	res.AccessToken, err = service.Tenant().RefreshToken(ctx, req.RefreshToken)

	if err != nil {
		res.Code = gerror.Code(err).Code()
		res.Message = gerror.Code(err).Message()
	} else {
		res.Code = consts.Success.Code()
		res.Message = consts.Success.Message()
	}
	r.Response.WriteJson(res)

	return
}

/*
Function Name: NewTokens
Description: Request new access and refresh tokens for a given tenant, password, and app key, and respond with the result.
Input Parameters:
   - ctx: Context for the request.
   - req: A NewTokensReq object containing the tenant ID, password, and app key for generating tokens.
Output:
   - res: A NewTokensRes object containing the access token, refresh token, and response details.
   - err: An error if the operation encounters an issue, otherwise nil.
Exception:
   - The function invokes the 'service.NewTokens' function to generate new access and refresh tokens for the specified tenant, password, and app key.
   - If an error occurs during the token generation process, the error is captured and included in the 'err' variable.
   - If tokens are generated successfully, the 'res' object is populated with the generated access and refresh tokens, status code, and message.
   - The response is written to the client's response in JSON format.
*/

func (c *ControllerV1) NewTokens(ctx context.Context, req *v1.NewTokensReq) (res *v1.NewTokensRes, err error) {
	r := g.RequestFromCtx(ctx)

	accessToken, refreshToken, err := service.Tenant().NewTokens(
		ctx,
		req.TenantID,
		req.Password,
		req.AppKey,
	)
	res = &v1.NewTokensRes{}
	if err != nil {
		res.Code = gerror.Code(err).Code()
		res.Message = gerror.Code(err).Message()
	} else {
		res.Code = consts.Success.Code()
		res.Message = consts.Success.Message()
		res.AccessToken = accessToken
		res.RefreshToken = refreshToken
	}

	r.Response.WriteJson(res)
	return
}

// NewTenant handles new tenant creation requests.
// It validates:
// - Tenant ID format using a regex
// - OldPass format using a regex
// If invalid, it returns an error response.
// Otherwise, it calls the service to create the tenant.
// Any error from the service is coded and returned.
// On success, the response is written.
// This provides request validation and service integration
// for new tenant creation.
func (c *ControllerV1) NewTenant(ctx context.Context, req *v1.NewTenantReq) (res *v1.NewTenantRes, err error) {
	res = &v1.NewTenantRes{}
	r := g.RequestFromCtx(ctx)
	if !c.isAdmin(ctx) {
		r.Response.WriteStatus(http.StatusUnauthorized)
		return

	}

	isMatch := utility.IsValidTenantName(req.TenantID)

	if !isMatch {
		res.Code = consts.ErrorFormatTenantID.Code()
		res.Message = consts.ErrorFormatTenantID.Message()
		r.Response.WriteJson(res)
		return
	}
	isMatch = utility.IsStrongPassword(req.Password)

	if !isMatch {
		res.Code = consts.ErrorFormatPassword.Code()
		res.Message = consts.ErrorFormatPassword.Message()
		r.Response.WriteJson(res)
		return

	}

	err = service.Tenant().CreateTenant(ctx, req.TenantID, req.Password)

	if err != nil {
		code := gerror.Code(err)
		res.Code = code.Code()
		res.Message = code.Message()
	} else {
		res.Code = consts.Success.Code()
		res.Message = consts.Success.Message()
	}

	r.Response.WriteJson(res)

	return
}

// ListTenants handles tenant list requests.
// It checks for admin role and PIN in headers.
// If missing, unauthorized response is sent.
// Otherwise, service is called to retrieve all tenants.
// Any error is coded and returned.
// On success, tenants data is set in the response.
// This provides tenant list API with auth and service integration.
func (c *ControllerV1) ListTenants(ctx context.Context, _ *v1.ListTenantsReq) (res *v1.ListTenantsRes, err error) {
	r := g.RequestFromCtx(ctx)

	if c.isAdmin(ctx) {
		data, err := service.Tenant().ListTenants(ctx)

		if err != nil {
			code := gerror.Code(err)
			res = &v1.ListTenantsRes{
				CodeMessage: v1.CodeMessage{
					Code:    code.Code(),
					Message: code.Message(),
				},
			}
			r.Response.WriteJson(res)
			return nil, nil
		}
		res = &v1.ListTenantsRes{CodeMessage: v1.CodeMessage{Code: consts.Success.Code(), Message: consts.Success.Message()}}
		ret := gjson.New(res)
		_ = ret.Set("data", data)
		r.Response.WriteJson(ret)

	} else {
		r.Response.WriteStatus(http.StatusUnauthorized)
	}

	return
}

// UpdateTenant handles tenant password update requests.
// It validates the new password format using regex.
// If invalid, an error response is returned.
// The service is called to update the tenant password.
// Any error from service is coded and returned.
// On success, a success response is returned.
// This provides password validation and service integration
// for handling tenant password updates.
func (c *ControllerV1) UpdateTenant(ctx context.Context, req *v1.UpdateTenantReq) (res *v1.UpdateTenantRes, err error) {
	res = &v1.UpdateTenantRes{}
	r := g.RequestFromCtx(ctx)

	isMatch := utility.IsStrongPassword(req.NewPassword)

	if !isMatch {

		res.Code = consts.ErrorFormatPassword.Code()
		res.Message = consts.ErrorFormatPassword.Message()
		r.Response.WriteJson(res)
		return
	}

	err = service.Tenant().UpdateTenant(ctx, req.TenantID, req.OldPassword, req.NewPassword, c.isAdmin(ctx))

	if err != nil {
		code := gerror.Code(err)
		res.Code = code.Code()
		res.Message = code.Message()
	} else {
		res.Code = consts.Success.Code()
		res.Message = consts.Success.Message()
	}
	r.Response.WriteJson(res)
	return
}

// RemoveTenant handles tenant removal requests.
// It first checks for admin role.
// If missing, unauthorized response is sent.
// Otherwise, the service is called to disable the tenant.
// Any error is coded and returned in the response.
// On success, a success response is returned.
// This provides service integration for tenant removal
// along with admin role based access control.
func (c *ControllerV1) RemoveTenant(ctx context.Context, req *v1.RemoveTenantReq) (res *v1.RemoveTenantRes, err error) {
	r := g.RequestFromCtx(ctx)
	res = &v1.RemoveTenantRes{}
	if c.isAdmin(ctx) {
		err = service.Tenant().DisableTenant(ctx, req.TenantID, req.Marked)
		if err != nil {
			code := gerror.Code(err)
			res.Code = code.Code()
			res.Message = code.Message()
		} else {
			res.Code = consts.Success.Code()
			res.Message = consts.Success.Message()

		}
		r.Response.WriteJson(res)

	} else {
		r.Response.WriteStatus(http.StatusUnauthorized)
	}

	return
}

// GenNewKey handles app key generation requests.
// It calls the service to generate a new app key for the tenant.
// Any error is coded and returned in the response.
// On success, the new key is set in the response.
// This provides service integration for generating new
// app keys for tenants.
func (c *ControllerV1) GenNewKey(ctx context.Context, req *v1.GenNewKeyReq) (res *v1.GenNewKeyRes, err error) {
	res = &v1.GenNewKeyRes{}
	r := g.RequestFromCtx(ctx)

	err, newKey := service.Tenant().GenNewAppKeyForTenant(ctx, req.TenantID, req.Password, c.isAdmin(ctx))
	if err != nil {
		code := gerror.Code(err)
		res.Code = code.Code()
		res.Message = code.Message()

	} else {
		res.Code = consts.Success.Code()
		res.Message = consts.Success.Message()
		res.NewKey = newKey

	}

	r.Response.WriteJson(res)

	return
}

/*
Function Name: inServices
Description: Validates a service based on the provided parameters within a controller.
Input Parameters:
   - ctx: Context for the operation.
   - microService: Miro service to be validated.
   - action: Action to be validated.
Output Parameters:
   - isExist: A boolean indicating whether the service exists in the configuration.
   - result: A boolean indicating the result of the validation.
Exception:
   - The function first obtains the Request object from the provided context.
   - It retrieves the 'consts.HeaderTenantID' and 'consts.HeaderAppKey' headers from the Request object.
   - It then calls the 'service.ValidateService' function to perform the validation, providing the context, tenant ID, app key, 'microService', and 'action'.
   - The function returns 'isExist' and 'result' as the output parameters.
*/

func (c *ControllerV1) inServices(ctx context.Context, microService, action string) (isExist bool, result bool) {
	r := g.RequestFromCtx(ctx)
	accounts := gstr.SplitAndTrim(r.GetHeader(consts.HeaderTenantID), "^")
	if len(accounts) == 0 {
		return
	}

	isExist, result = service.ValidateService(
		ctx,
		accounts[0],
		r.GetHeader(consts.HeaderAppKey),
		microService,
		action,
	)

	return
}

/*
Function Name: AuthTenant
Description: Authorizes a tenant's access to an interface based on the provided parameters within a controller.
Input Parameters:
   - ctx: Context for the operation.
   - req: AuthTenantReq containing the service and action to be authorized.
Output Parameters:
   - res: AuthTenantRes containing the result of the authorization.
   - err: An error indicating any issues during the authorization process.
Exception:
   - The function first obtains the Request object from the provided context.
   - If the user is an admin, the function returns an HTTP OK response (200).
   - If the user is not an admin, the function checks whether the request is from an external service.
   - If the request is from a configured external service, it checks whether the provided key is valid and whether the service and action are permitted.
   - If the request is valid, the function returns an HTTP OK response (200); otherwise, it returns an HTTP Unauthorized response (401).
   - If the request is not from an external service, the function checks whether the service and action are in the list of excluded actions.
   - If the service and action are in the list of excluded actions, the function returns an HTTP Unauthorized response (401).
   - If none of the above conditions are met, the function retrieves the token from the Authorization header and calls the 'service.AuthTenant' function for authorization.
   - If authorization is successful, the function returns an HTTP OK response (200); otherwise, it returns an HTTP Unauthorized response (401).
*/

func (c *ControllerV1) AuthTenant(ctx context.Context, req *v1.AuthTenantReq) (res *v1.AuthTenantRes, err error) {
	r := g.RequestFromCtx(ctx)
	// admin 不做检查所有action 都可以执行
	if c.isAdmin(ctx) {
		r.Response.WriteStatus(http.StatusOK)
		return
	} else if !c.isActiveTenant(ctx) {
		// the tenant is deactive tenant then return unauthorized

		g.Log().Cat(consts.DEBUG).Debugf(ctx, "The tenant %s is not active", r.GetHeader(consts.HeaderTenantID))
		r.Response.WriteStatus(http.StatusUnauthorized)
		return
	}

	// 如果不是admin 则检查是否为外部服务调用。 如果是在设定的服务列表中，但是 key 不对或者不在准许的服务和action内则返回false
	isExist, result := c.inServices(ctx, req.Service, req.Action)
	if isExist {
		if !result {
			r.Response.WriteStatus(http.StatusUnauthorized)
		} else {
			r.Response.WriteStatus(http.StatusOK)
		}
		return
	}

	// 不是admin，检查service.action 是否在排除之外的动作。 如果包含在不可执行的动作，则直接返回401
	vActions, _ := g.Cfg().Get(ctx, fmt.Sprintf("permissions.exclude.%s", req.Service))
	if garray.NewStrArrayFrom(vActions.Strings()).Contains(req.Action) {
		g.Log().Debugf(ctx, "the tenant cannot execute the interface of %s.%s", req.Service, req.Action)
		r.Response.WriteStatus(http.StatusUnauthorized)
		return
	}

	token := gstr.TrimLeftStr(r.GetHeader("Authorization"), "Bearer ")

	err = service.Tenant().AuthTenant(ctx, gstr.Trim(token))

	if err != nil {
		r.Response.WriteStatus(http.StatusUnauthorized)
	} else {
		r.Response.WriteStatus(http.StatusOK)
	}

	return
}

func (c *ControllerV1) GetLLMs(ctx context.Context, req *v1.GetLLMsReq) (res *v1.GetLLMsRes, err error) {
	if c.isAdmin(ctx) {
		ret := service.DataStore().GetLLMs(ctx, req.Detail)
		res = &v1.GetLLMsRes{}
		res.Code = consts.Success.Code()
		res.Message = consts.Success.Message()
		res.Data = make([]*model.LLMConfiguration, 0)
		if ret != nil {
			for _, llm := range ret.GetJsons("data") {
				var llmParams *model.LLMConfiguration
				_ = llm.Scan(&llmParams)
				res.Data = append(res.Data, llmParams)
			}
		}

		g.RequestFromCtx(ctx).Response.WriteJson(res)
	} else {
		g.RequestFromCtx(ctx).Response.WriteStatus(http.StatusUnauthorized)
	}

	return
}

func (c *ControllerV1) GetLLMByTenant(ctx context.Context, req *v1.GetLLMByTenantReq) (res *v1.GetLLMByTenantRes, err error) {
	if c.isAdmin(ctx) {
		ret := service.DataStore().GetTenantLLM(ctx, req.TenantID)
		res = &v1.GetLLMByTenantRes{}
		res.Code = consts.Success.Code()
		res.Message = consts.Success.Message()
		for _, llm := range ret.GetJsons("data") {
			var llmParams *model.LLMConfiguration
			_ = llm.Scan(&llmParams)
			res.Data = append(res.Data, llmParams)
		}
		g.RequestFromCtx(ctx).Response.WriteJson(res)
	} else {
		g.RequestFromCtx(ctx).Response.WriteStatus(http.StatusUnauthorized)
	}

	return
}

func (c *ControllerV1) AssignLLMToTenant(ctx context.Context, req *v1.AssignLLMToTenantReq) (res *v1.AssignLLMToTenantRes, err error) {
	if c.isAdmin(ctx) {
		for _, llmName := range req.LLMs {
			err = service.DataStore().AssignLLMToTenant(ctx, req.TenantID, llmName)
			if err != nil {
				break
			}
		}
		res = &v1.AssignLLMToTenantRes{}
		if err != nil {
			res.Code = gerror.Code(err).Code()
			res.Message = gerror.Code(err).Message()
		} else {
			res.Code = consts.Success.Code()
			res.Message = consts.Success.Message()
		}

		g.RequestFromCtx(ctx).Response.WriteJson(res)
	} else {
		g.RequestFromCtx(ctx).Response.WriteStatus(http.StatusUnauthorized)
	}

	return
}

func (c *ControllerV1) RemoveTenantLLM(ctx context.Context, req *v1.RemoveTenantLLMReq) (res *v1.RemoveTenantLLMRes, err error) {
	if c.isAdmin(ctx) {
		err = service.DataStore().RemoveTenantLLM(ctx, req.TenantID, req.LLMs)
		res = &v1.RemoveTenantLLMRes{}
		if err != nil {
			res.Code = gerror.Code(err).Code()
			res.Message = gerror.Code(err).Message()
		} else {
			res.Code = consts.Success.Code()
			res.Message = consts.Success.Message()
		}
		g.RequestFromCtx(ctx).Response.WriteJson(res)
	} else {
		g.RequestFromCtx(ctx).Response.WriteStatus(http.StatusUnauthorized)
	}

	return
}

func (c *ControllerV1) GetLLMTenantList(ctx context.Context, req *v1.GetLLMTenantListReq) (res *v1.GetLLMTenantListRes, err error) {
	r := g.RequestFromCtx(ctx)
	if c.isAdmin(ctx) {
		res = &v1.GetLLMTenantListRes{}
		res.Code = consts.Success.Code()
		res.Message = consts.Success.Message()
		res.Data = service.DataStore().GetLLMTenantsList(ctx)

		r.Response.WriteJson(res)
	} else {
		r.Response.WriteStatus(http.StatusUnauthorized)
	}
	return
}

func (c *ControllerV1) DeactiveTenant(ctx context.Context, req *v1.DeactivateTenantReq) (res *v1.DeactivateTenantRes, err error) {
	r := g.RequestFromCtx(ctx)
	if c.isAdmin(ctx) {
		// mark for deletion
		err = service.Tenant().DisableTenant(ctx, req.TenantID, true)
		res = &v1.DeactivateTenantRes{}

		if err != nil {
			res.Code = gerror.Code(err).Code()
			res.Message = gerror.Code(err).Message()
		} else {
			res.Code = consts.Success.Code()
			res.Message = consts.Success.Message()
		}
		r.Response.WriteJson(res)

	} else {
		r.Response.WriteStatus(http.StatusUnauthorized)
	}
	return
}

func (c *ControllerV1) ActiveTenant(ctx context.Context, req *v1.ActiveTenantReq) (res *v1.ActiveTenantRes, err error) {
	r := g.RequestFromCtx(ctx)
	if c.isAdmin(ctx) {
		err = service.Tenant().ActivateTenant(ctx, req.TenantID)
		res = &v1.ActiveTenantRes{}
		if err != nil {
			res.Code = gerror.Code(err).Code()
			res.Message = gerror.Code(err).Message()
		} else {
			res.Code = consts.Success.Code()
			res.Message = consts.Success.Message()
		}
		r.Response.WriteJson(res)

	} else {
		r.Response.WriteStatus(http.StatusUnauthorized)
	}
	return
}
func (c *ControllerV1) SetTenantParams(ctx context.Context, req *v1.SetTenantParamsReq) (res *v1.SetTenantParamsRes, err error) {
	r := g.RequestFromCtx(ctx)
	res = &v1.SetTenantParamsRes{}
	if c.isAdmin(ctx) {
		err = service.DataStore().SetTenantParams(ctx, req.TenantID, req.Params.MustToJsonIndentString())
		if err != nil {
			res.Code = gerror.Code(err).Code()
			res.Message = gerror.Code(err).Message()

		} else {
			res.Code = consts.Success.Code()
			res.Message = consts.Success.Message()
		}
		r.Response.WriteJsonExit(res)
	} else {
		r.Response.WriteStatusExit(http.StatusUnauthorized)
	}

	return
}

func (c *ControllerV1) GetTenantParams(ctx context.Context, req *v1.GetTenantParamsReq) (res *v1.GetTenantParamsRes, err error) {
	r := g.RequestFromCtx(ctx)
	res = &v1.GetTenantParamsRes{}
	if c.isAdmin(ctx) {
		params, err := service.DataStore().GetTenantParams(ctx, req.TenantID)
		if err != nil {
			res.Code = gerror.Code(err).Code()
			res.Message = gerror.Code(err).Message()

		} else {
			res.Code = consts.Success.Code()
			res.Message = consts.Success.Message()
			res.Data = gjson.New(params)
		}
		r.Response.WriteJsonExit(res)
	} else {
		r.Response.WriteStatusExit(http.StatusUnauthorized)
	}

	return

}

// CheckOrCreateTenant 檢查或創建租戶
// 檢查租戶是否存在，如果不存在則創建新租戶並創建分區表
func (c *ControllerV1) CheckOrCreateTenant(ctx context.Context, req *v1.CheckOrCreateTenantReq) (res *v1.CheckOrCreateTenantRes, err error) {
	res = &v1.CheckOrCreateTenantRes{}
	r := g.RequestFromCtx(ctx)

	// 參數驗證
	if req.TenantID == "" {
		res.Code = consts.ErrorGeneral.Code()
		res.Message = "tenant_id cannot be empty"
		r.Response.WriteJson(res)
		return
	}

	g.Log().Cat(consts.INFO).Infof(ctx, "Processing check or create tenant request for: %s", req.TenantID)

	// 調用業務邏輯
	err = service.Tenant().CheckOrCreateTenant(ctx, req.TenantID)
	if err != nil {
		code := gerror.Code(err)
		res.Code = code.Code()
		res.Message = code.Message()
		g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to check or create tenant %s: %v", req.TenantID, err)
	} else {
		res.Code = consts.Success.Code()
		res.Message = consts.Success.Message()
		g.Log().Cat(consts.INFO).Infof(ctx, "Successfully processed tenant: %s", req.TenantID)
	}

	r.Response.WriteJson(res)
	return
}

func (c *ControllerV1) Remark(ctx context.Context, req *v1.RemarkReq) (res *v1.RemarkRes, err error) {
	r := g.RequestFromCtx(ctx)
	if c.isAdmin(ctx) {

		err = service.Tenant().Remark(ctx, req.TenantID, req.Remark)
		res = &v1.RemarkRes{}
		res.Code = consts.Success.Code()
		res.Message = consts.Success.Message()
		if err != nil {
			res.Code = gerror.Code(err).Code()
			res.Message = gerror.Code(err).Message()
		}
		r.Response.WriteJson(res)

	} else {
		r.Response.WriteStatus(http.StatusUnauthorized)
	}

	return
}

// GetSysInstruction 獲取租戶的系統指令
// 此方法處理獲取指定租戶系統指令的請求
// 需要管理員權限才能執行此操作
// 調用 DataStore 服務獲取系統指令內容
func (c *ControllerV1) GetSysInstruction(ctx context.Context, req *v1.GetSysInstructionReq) (res *v1.GetSysInstructionRes, err error) {
	r := g.RequestFromCtx(ctx)
	res = &v1.GetSysInstructionRes{}

	// 檢查管理員權限
	if !c.isAdmin(ctx) {
		r.Response.WriteStatusExit(http.StatusUnauthorized)
		return
	}

	// 調用 DataStore 服務獲取系統指令
	sysInstruction, err := service.DataStore().GetSysInstructionByTenantID(ctx, req.TenantID)
	if err != nil {
		res.Code = gerror.Code(err).Code()
		res.Message = gerror.Code(err).Message()
		g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to get system instruction for tenant %s: %v", req.TenantID, err)
	} else {
		res.Code = consts.Success.Code()
		res.Message = consts.Success.Message()
		res.Data = sysInstruction
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Successfully retrieved system instruction for tenant: %s", req.TenantID)
	}

	r.Response.WriteJsonExit(res)
	return
}

// SetSysInstruction 設置租戶的系統指令
// 此方法處理設置指定租戶系統指令的請求
// 需要管理員權限才能執行此操作
// 調用 DataStore 服務寫入系統指令內容
func (c *ControllerV1) SetSysInstruction(ctx context.Context, req *v1.SetSysInstructionReq) (res *v1.SetSysInstructionRes, err error) {
	r := g.RequestFromCtx(ctx)
	res = &v1.SetSysInstructionRes{}

	// 檢查管理員權限
	if !c.isAdmin(ctx) {
		r.Response.WriteStatusExit(http.StatusUnauthorized)
		return
	}

	// 驗證系統指令參數
	if req.SystemInstruction == nil {
		res.Code = consts.ErrorGeneral.Code()
		res.Message = "System instruction cannot be empty"
		r.Response.WriteJsonExit(res)
		return
	}

	// 調用 DataStore 服務寫入系統指令
	err = service.DataStore().WriteSystemInstruction(ctx, req.TenantID, req.SystemInstruction)
	if err != nil {
		res.Code = gerror.Code(err).Code()
		res.Message = gerror.Code(err).Message()
		g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to set system instruction for tenant %s: %v", req.TenantID, err)
	} else {
		res.Code = consts.Success.Code()
		res.Message = consts.Success.Message()
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Successfully set system instruction for tenant: %s", req.TenantID)
	}

	r.Response.WriteJsonExit(res)
	return
}
