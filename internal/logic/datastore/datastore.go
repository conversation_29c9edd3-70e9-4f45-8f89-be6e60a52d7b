package datastore

import (
	"context"
	"fmt"
	"github.com/gogf/gf/container/gvar"
	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/container/gset"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/net/gsvc"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/os/gtimer"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"tenants/internal/consts"
	"tenants/internal/logic/messageQ"
	"tenants/internal/model"
	"tenants/internal/service"
)

type sDataStore struct {
	client *gclient.Client
}

func init() {
	service.RegisterDataStore(New())
}

func New() service.IDataStore {
	d := &sDataStore{
		client: g.Client(),
	}
	d.client.SetDiscovery(gsvc.GetRegistry())

	ctx := gctx.GetInitCtx()

	g.Go(ctx, func(ctx context.Context) {
		<-messageQ.ReadyMessageQ
		g.Log().Notice(ctx, "ready to check db ")
		d.checkDatabase(ctx)
		d.checkCreateTable(ctx)
		d.checkProfile(ctx)
		d.checkPartitionTables(ctx) // 先执行一次

		gtimer.SetInterval(ctx, gtime.D*15, d.checkPartitionTables) // 每15天举行一次

	}, func(ctx context.Context, exception error) {
		g.Log().Cat(consts.ERROR).Error(ctx, gerror.Stack(exception))
	})

	return d
}

func (s *sDataStore) checkPartitionTables(ctx context.Context) {
	g.Log().Cat(consts.INFO).Info(ctx, "Starting partition tables check for all tenants")

	// 獲取所有租戶信息
	req := model.GetContentsReq{
		Schema: consts.DataBaseDSH,
		Table:  consts.TableTenant,
		Fields: g.Slice{"tenant_Id"},
	}

	res, err := s.getContents(ctx, req)
	if err != nil {
		g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to get tenant list: %v", err)
		return
	}

	if len(res.Contents) == 0 {
		g.Log().Cat(consts.WARNING).Warning(ctx, "No tenants found")
		return
	}

	// 為每個租戶創建分區表
	successCount := 0
	errorCount := 0

	for _, content := range res.Contents {
		tenantID := gconv.String(content["tenant_Id"])
		if tenantID == "" {
			g.Log().Cat(consts.WARNING).Warning(ctx, "Found empty tenant_Id, skipping")
			continue
		}

		if err := s.createPartitionTablesForTenant(ctx, tenantID); err != nil {
			g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to create partition tables for tenant %s: %v", tenantID, err)
			errorCount++
		} else {
			successCount++
		}
	}

	g.Log().Cat(consts.INFO).Infof(ctx, "Partition tables check completed. Success: %d, Errors: %d", successCount, errorCount)
}

// generateTableScripts 生成表腳本列表
// 根據當前時間生成從當前月份到年底的表腳本，如果是12月還會生成下一年1-12月的表腳本
func (s *sDataStore) generateTableScripts(script string) []string {
	batchScript := make([]string, 0)
	nowTime := gtime.Now()
	year := nowTime.Year()
	month := nowTime.Month()

	// 生成從當前月份到年底的表腳本
	for i := month; i <= 12; i++ {
		suffix := fmt.Sprintf("%04d_%02d", year, i)
		newScript := fmt.Sprintf(script, suffix)
		batchScript = append(batchScript, newScript)
	}

	// 如果當前月份是12月，則創建下一年1-12月的表
	if month == 12 {
		nextYear := year + 1
		for i := 1; i <= 12; i++ {
			suffix := fmt.Sprintf("%04d_%02d", nextYear, i)
			newScript := fmt.Sprintf(script, suffix)
			batchScript = append(batchScript, newScript)
		}
	}

	return batchScript
}

// executeTableScripts 執行表腳本
// 為指定租戶執行批量 SQL 腳本
func (s *sDataStore) executeTableScripts(ctx context.Context, tenantID string, scripts []string) error {
	if len(scripts) == 0 {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "No scripts to execute for tenant: %s", tenantID)
		return nil
	}

	reqBatch := model.BatchSqlReq{
		Schema:  tenantID,
		SQLList: scripts,
	}

	result, err := s.ExecuteBatchSql(ctx, reqBatch)
	if err != nil {
		g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to execute batch SQL for tenant %s: %v", tenantID, err)
		return err
	}

	if result != nil {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Create table result for tenant %s: %v",
			tenantID, gjson.New(result).MustToJsonString())
	}

	return nil
}

// createPartitionTablesForTenant 為特定租戶創建分區表
// 入參為 tenant_id，為該租戶創建所有配置的分區表
func (s *sDataStore) createPartitionTablesForTenant(ctx context.Context, tenantID string) error {
	if tenantID == "" {
		return gerror.NewCode(consts.ErrorGeneral, "tenant_id cannot be empty")
	}

	g.Log().Cat(consts.INFO).Infof(ctx, "Creating partition tables for tenant: %s", tenantID)

	// 從配置中獲取表腳本
	vTables, err := g.Cfg().Get(ctx, "tables")
	if err != nil {
		g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to get tables config: %v", err)
		return gerror.WrapCode(consts.ErrorGeneral, err, "Failed to get tables config")
	}

	// 遍歷所有表腳本
	for _, script := range vTables.Array() {
		mapScript := gvar.New(script).MapStrStr()
		createTableScript := mapScript["script"]

		if createTableScript == "" {
			g.Log().Cat(consts.WARNING).Warningf(ctx, "Empty script found in tables config")
			continue
		}

		// 生成表腳本
		scripts := s.generateTableScripts(createTableScript)

		// 執行表腳本
		if err := s.executeTableScripts(ctx, tenantID, scripts); err != nil {
			g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to create tables for tenant %s: %v", tenantID, err)
			return err
		}
	}

	g.Log().Cat(consts.INFO).Infof(ctx, "Successfully created partition tables for tenant: %s", tenantID)
	return nil
}

func (s *sDataStore) sendMessage(ctx context.Context, action string, data []byte, additionalRouteKeys ...string) (err error) {
	return service.MessageQ().Send(
		ctx,
		consts.RouteKeyMariaDB,
		action,
		data,
		additionalRouteKeys...,
	)
}

func (s *sDataStore) getLLMNameFromCache(ctx context.Context) []string {

	retLLMNames := make([]string, 0)
	keys, _ := g.Redis().Keys(ctx, consts.LLMParamsKeyPattern)
	for _, key := range keys {
		v := gstr.Split(key, ":")
		if len(v) >= 2 {
			retLLMNames = append(retLLMNames, v[1])
		}

	}

	return retLLMNames
}

// checkProfile is a method of the sDataStore struct.
// It sets an interval timer to check the llms (LLMConfiguration instances) at a specified interval.
// The interval is retrieved from the configuration using the key "llm.check_llms_interval" and defaults to "1m" (1 minute) if not found.
// At each interval, it retrieves the llms from the configuration using the key "llms" and scans them into a slice of LLMConfiguration pointers.
// If an error occurs during scanning, it logs the error and exits the timer.
// For each llm in the slice, it checks if a cache object with the same name as the llm exists.
// If such a cache object exists and is not nil, it checks if the llm and the cache object are the same using the IsSame method of the LLMConfiguration struct.
// If they are not the same, it updates the cache object with the llm and writes the llm into the database using the WriteLLMParams method of the sDataStore struct.
// If no cache object with the same name as the llm exists, it adds a new cache object with the llm and writes the llm into the database using the WriteLLMParams method of the sDataStore struct.
//
// Parameters:
// ctx : context.Context - The context in which the method is called.
func (s *sDataStore) checkProfile(ctx context.Context) {
	g.Log().Cat(consts.INFO).Info(ctx, "check profile ...")
	interval, _ := g.Cfg().Get(ctx, "llm.check_llms_interval", "1m")
	g.Log().Cat(consts.INFO).Infof(ctx, "llm check interval: %v", interval)

	gtimer.SetInterval(ctx, interval.Duration(), func(ctx context.Context) {
		// check llms
		var llms []*model.LLMConfiguration
		vLLMs, _ := g.Cfg().Get(ctx, "llms")
		if err := vLLMs.Scan(&llms); err != nil {
			g.Log().Cat(consts.ERROR).Error(ctx, err)
			gtimer.Exit()
		}
		llmNames := gset.NewStrSet(true)

		for _, llm := range llms {
			llmNames.Add(llm.LLMName)
			// If there is a llm with the same name in the cache, check whether they are the same. If they are different, update the object in the cache.
			// If there is no llm with the same name in the cache, a new cache object is added. And write the new llm into the database.
			if isExist, _ := g.Redis().Exists(ctx, fmt.Sprintf(consts.LLMParamsKeyFormat, llm.LLMName)); isExist == 1 {
				_llm, _ := g.Redis().Get(ctx, fmt.Sprintf(consts.LLMParamsKeyFormat, llm.LLMName))
				if !_llm.IsNil() {
					if !llm.IsSame(_llm) {
						g.Log().Cat(consts.DEBUG).Debugf(ctx, "LLM Name :%v  is modified", llm.LLMName)
						// update
						if _, e := g.Redis().Set(ctx, fmt.Sprintf(consts.LLMParamsKeyFormat, llm.LLMName), llm); e != nil {
							g.Log().Cat(consts.ERROR).Error(ctx, e)
						}
						_ = s.WriteLLMParams(ctx, llm)
					}
				}
			} else {
				if _, e := g.Redis().Set(ctx, fmt.Sprintf(consts.LLMParamsKeyFormat, llm.LLMName), llm); e != nil {
					g.Log().Cat(consts.ERROR).Error(ctx, e)
				}

				_ = s.WriteLLMParams(ctx, llm)

			}
		}

		llmNamesInCache := gset.NewStrSetFrom(s.getLLMNameFromCache(ctx), true)

		diff := llmNamesInCache.Diff(llmNames)
		// Check the set llm and the llm currently in the cache. If the llm in the cache is no longer in the configuration file, remove it from the cache and the database.
		for _, _llmName := range diff.Slice() {
			_ = s.RemoveLLMParams(ctx, _llmName)
			// remove from cache
			_, _ = g.Redis().Del(ctx, fmt.Sprintf(consts.LLMParamsKeyFormat, _llmName))
		}
		if diff.Size() > 0 {
			// if there is a change, reload the tenant llm corresponding relationship map into the cache
			keys, _ := g.Redis().Keys(ctx, consts.TenantLLMKeyPattern)
			if len(keys) > 0 {
				_, _ = g.Redis().Del(ctx, keys...)
				s.loadTenantLLMToCache(ctx)
			}
		}
	})

	s.loadTenantLLMToCache(ctx)

	gtimer.SetInterval(ctx, gtime.D, s.loadTenantLLMToCache)
}

func (s *sDataStore) ExecuteBatchSql(ctx context.Context, in model.BatchSqlReq) (res *model.BatchSqlRes, err error) {
	vServiceName, _ := g.Cfg().Get(ctx, "system.data_service.name", "dsh.svc")
	vScheme, _ := g.Cfg().Get(ctx, "system.data_service.scheme", "http")
	url := fmt.Sprintf("%s://%s%s", vScheme.String(), vServiceName, consts.UriExecuteBatchSQL)

	response, err := s.client.ContentJson().SetHeader(consts.XHeaderService, consts.ServiceName).
		Post(ctx, url, in)
	if err != nil {
		return nil, err
	}
	defer func(response *gclient.Response) {
		_ = response.Close()
	}(response)
	strResponse := response.ReadAllString()
	if !gjson.Valid(strResponse) {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Response: %v", strResponse)
		err = gerror.NewCode(consts.ErrorGeneral, "response is not valid")
		return

	}
	_ = gjson.New(strResponse).Scan(&res)
	if res != nil && res.Code != consts.Success.Code() {
		g.Log().Cat(consts.ERROR).Error(ctx, res.Message)
		err = gerror.New(res.Message)
		return
	}
	return
}
func (s *sDataStore) getContents(ctx context.Context, in model.GetContentsReq) (out *model.GetContentsRes, err error) {
	vServiceName, _ := g.Cfg().Get(ctx, "system.data_service.name", "dsh.svc")
	vScheme, _ := g.Cfg().Get(ctx, "system.data_service.scheme", "http")
	url := fmt.Sprintf("%s://%s%s", vScheme.String(), vServiceName, consts.UriGetContent)
	response, err := s.client.ContentJson().SetHeader(consts.XHeaderService, consts.ServiceName).
		Post(ctx, url, in)
	if err != nil {
		return nil, err
	}
	defer func(response *gclient.Response) {
		_ = response.Close()
	}(response)
	strResponse := response.ReadAllString()

	if !gjson.Valid(strResponse) {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Response: %v", strResponse)
		err = gerror.NewCode(consts.ErrorGeneral, "response is not valid")
		return
	}

	_ = gjson.New(strResponse).Scan(&out)

	if out != nil && out.Code != consts.Success.Code() {
		err = gerror.New(out.Message)
		return
	}

	return
}

// loadTenantLLMToCache is a method of the sDataStore struct.
// It loads the tenant LLM (Language Learning Model) data from the database to the cache.
//
// The method logs the start of the loading process and then retrieves all records from the TenantLLM table in the Quizto database.
// Each record contains a tenant_id and  a  llm_name.
// If an error occurs during the retrieval of the records, it logs the error.
// Otherwise, it iterates over all records and for each record, it retrieves the tenant_id and the llm_name.
// It then sets a cache object with a key formatted as TenantLLMKeyFormat and the tenant_id as the value, and the llm_name as the value.
// The cache object is set to expire after a duration of one day.
//
// Parameters:
// ctx : context.Context - The context in which the method is called.
func (s *sDataStore) loadTenantLLMToCache(ctx context.Context) {
	// Log the start of the loading process
	g.Log().Cat(consts.INFO).Info(ctx, "load tenant llm to cache...")

	// Retrieve all records from the TenantLLM table in the Quizto database
	req := model.GetContentsReq{
		Schema: consts.DataBaseDSH,
		Table:  consts.TableTenantLLM,
		Fields: g.Slice{"tenant_id", "llm_name"},
	}
	res, err := s.getContents(ctx, req)
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}

	for _, record := range res.Contents {
		tenantId := gconv.String(record["tenant_id"])
		llmName := gconv.String(record["llm_name"])
		s.setLLMTenantToCache(ctx, tenantId, llmName)
	}

}

func (s *sDataStore) checkDatabase(ctx context.Context) {

	mqMessage := &model.MQMessage{
		Schema: consts.DataBaseDSH,
	}
	if e := s.sendMessage(
		ctx,
		consts.ActionCreateSchema,
		gconv.Bytes(mqMessage.String()),
	); e != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, e)
	}
}

// checkCreateTable is a method of the sDataStore struct.
// It checks if the necessary tables exist in the database and creates them if they do not.
// It first retrieves all databases and checks if the database with the name specified in the constant DataBaseDSH exists.
// If such a database exists, it retrieves all tables in the database and checks if the tables with the names specified in the constants TableLLMParams and TableTenantLLM exist.
// If the TableLLMParams table does not exist, it creates the table by executing the SQL script specified in the constant ScriptLLMParams.
// If the TableTenantLLM table does not exist, it creates the table by executing the SQL script specified in the constant ScriptTenantLLM.
// If an error occurs during any of these operations, it logs the error and returns it wrapped with the error code specified in the constant ErrorGeneral.
//
// Parameters:
// ctx : context.Context - The context in which the method is called.
//
// Returns:
// err : error - The error that occurred during
func (s *sDataStore) checkCreateTable(ctx context.Context) {

	// Log the start of the database and tables check
	g.Log().Cat(consts.INFO).Info(ctx, "Check database and tables ... ")

	mqMessage := &model.MQMessage{
		Schema: consts.DataBaseDSH,
		Data:   consts.TableNameScript,
	}

	if e := s.sendMessage(
		ctx,
		consts.ActionCreateTable,
		gconv.Bytes(mqMessage.String()),
	); e != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, e)
	}

}

func (s *sDataStore) isLLMExist(ctx context.Context, llmName string) (err error) {
	// check if LLM in cache
	if isExist, _ := g.Redis().Exists(ctx, fmt.Sprintf(consts.LLMParamsKeyFormat, llmName)); isExist == 1 {
		return
	} else {
		req := model.GetContentsReq{
			Schema:    consts.DataBaseDSH,
			Table:     consts.TableLLMParams,
			WhereCond: "llm_name = ? ",
			Params:    g.Slice{llmName},
			Fields:    g.Slice{"llm_name"},
		}
		out, err := s.getContents(ctx, req)
		if err != nil {
			return err
		}
		if len(out.Contents) == 0 {
			return gerror.NewCode(consts.ErrorGeneral, "LLM not exist")
		}

	}

	return
}

// WriteLLMParams is a method of the sDataStore struct.
// It writes the provided LLM parameters into the database.
// It first logs the LLM parameters and then scans them into an LLMConfiguration struct.
// It then starts a transaction and attempts to replace the existing LLM parameters in the database with the new ones.
// If an error occurs during any of these operations, it logs the error and returns it wrapped with the error code specified in the constant ErrorGeneral.
//
// Parameters:
// ctx : context.Context - The context in which the method is called.
// llmParams : any - The new LLM parameters to be written into the database.
//
// Returns:
// err : error - The error that occurred during the execution of the method, if any.
func (s *sDataStore) WriteLLMParams(ctx context.Context, llmParams any) (err error) {
	// Log the LLM parameters
	g.Log().Cat(consts.INFO).Info(ctx, "Write LLM Params :  ", gjson.New(llmParams).MustToJsonIndentString())
	llm := &model.LLMConfiguration{}
	_ = gconv.Scan(llmParams, llm)

	mqMessage := &model.MQMessage{
		Schema:          consts.DataBaseDSH,
		Table:           consts.TableLLMParams,
		WhereConditions: "llm_name = ? ",
		WhereParams:     g.Slice{llm.LLMName},
		Data:            llmParams,
	}

	err = s.sendMessage(
		ctx,
		consts.ActionUpdateOrInsert,
		gconv.Bytes(mqMessage.String()),
	)
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, "Write LLM Params :  ", err)
	}

	return
}

// RemoveLLMParams is a method of the sDataStore struct.
// It removes the LLM (Language Learning Model) parameters from the database.
//
// The method first starts a transaction and attempts to delete the LLM parameters from the TableLLMParams table in the database.
// If an error occurs during this operation, it logs the error and returns it wrapped with the error code specified in the constant ErrorGeneral.
// If the operation is successful, it starts another transaction and attempts to delete the LLM from the TableTenantLLM table in the database.
// If an error occurs during this operation, it logs the error and returns it wrapped with the error code specified in the constant ErrorGeneral.
//
// Parameters:
// ctx : context.Context - The context in which the method is called.
// llmName : string - The name of the LLM to be removed from the database.
//
// Returns:
// err : error - The error that occurred during the execution of the method, if any.
func (s *sDataStore) RemoveLLMParams(ctx context.Context, llmName string) (err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Remove LLM Params : llm name %v", llmName)
	mqMessage := &model.MQMessage{
		Schema:          consts.DataBaseDSH,
		Table:           consts.TableLLMParams,
		WhereConditions: "llm_name = ? ",
		WhereParams:     g.Slice{llmName},
	}

	err = s.sendMessage(
		ctx,
		consts.ActionDelete,
		gconv.Bytes(mqMessage.String()),
	)

	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, "Remove LLM Params from table %q:  ", consts.TableLLMParams, err)
	}

	mqMessage.Table = consts.TableTenantLLM

	err = s.sendMessage(
		ctx,
		consts.ActionDelete,
		gconv.Bytes(mqMessage.String()),
	)

	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, "Remove LLM Params from table %q:  ", consts.TableTenantLLM, err)
	}

	return
}

func (s *sDataStore) GetSysInstructionByTenantID(ctx context.Context, tenantID string) (prompts *model.SystemInstruction, err error) {
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "Get Prompt By Tenant ID : tenant id %v ", tenantID)
	key := fmt.Sprintf(consts.TenantPromptKeyFormat, tenantID)
	v, _ := g.Redis().Get(ctx, key)
	if v != nil && !v.IsNil() {
		_ = v.Struct(&prompts)
		if prompts != nil && !prompts.IsEmpty() {
			return
		}
	}

	req := model.GetContentsReq{
		Schema:    consts.DataBaseDSH,
		Table:     consts.TableTenantPrompt,
		WhereCond: "tenant_id = ?",
		Params:    g.Slice{tenantID},
		Fields:    g.Slice{"prompt"},
	}

	res, err := s.getContents(ctx, req)
	if err != nil {
		return
	}
	if len(res.Contents) > 0 {

		_ = gconv.Struct(res.Contents[0]["prompt"], &prompts)
		_ = g.Redis().SetEX(ctx, key, gjson.New(prompts).MustToJsonString(), gconv.Int64(gtime.D.Seconds()))
	}

	return

}

func (s *sDataStore) WriteSystemInstruction(ctx context.Context, tenantID string, prompts *model.SystemInstruction) (err error) {
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "Write System Instruction : tenant id %v , prompts %v", tenantID, gjson.New(prompts).MustToJsonIndentString())
	mqMessage := &model.MQMessage{
		Schema:          consts.DataBaseDSH,
		Table:           consts.TableTenantPrompt,
		Data:            g.Map{"tenant_id": tenantID, "prompt": gjson.New(prompts).MustToJsonString()},
		WhereConditions: "tenant_id = ?",
		WhereParams:     g.Slice{tenantID},
	}

	err = s.sendMessage(
		ctx,
		consts.ActionUpdateOrInsert,
		gconv.Bytes(mqMessage.String()),
		consts.RouteKeyPayloadChanged,
		fmt.Sprintf("tenant_id:%s", tenantID),
	)

	//write prompt to catch
	key := fmt.Sprintf(consts.TenantPromptKeyFormat, tenantID)
	_ = g.Redis().SetEX(ctx, key, gjson.New(prompts).MustToJsonString(), gconv.Int64(gtime.D.Seconds()))

	return
}

// AssignLLMToTenant is a method of the sDataStore struct.
// It assigns a Language Learning Model (LLM) to a tenant in the database and updates the cache.
//
// The method first logs the tenant ID and the LLM name.
// It then starts a transaction and attempts to replace the existing LLM of the tenant in the database with the new one.
// If an error occurs during this operation, it logs the error and returns it wrapped with the error code specified in the constant ErrorGeneral.
// If the operation is successful, it updates the cache with the new LLM of the tenant using the setLLMTenantToCache method of the sDataStore struct.
//
// Parameters:
// ctx : context.Context - The context in which the method is called.
// tenantId : string - The ID of the tenant to which the LLM should be assigned.
// llmName : string - The name of the LLM to be assigned to the tenant.
//
// Returns:
// err : error - The error that occurred during the execution of the method, if any.
func (s *sDataStore) AssignLLMToTenant(ctx context.Context, tenantId string, llmName string) (err error) {
	// Log the tenant ID and the LLM name
	g.Log().Cat(consts.INFO).Infof(ctx, "Assign LLM to Tenant : tenant id %v llm name %v ", tenantId, llmName)
	if s.isLLMExist(ctx, llmName) != nil {
		err = gerror.NewCode(consts.ErrorGeneral, "LLM not exist")
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}

	mqMessage := &model.MQMessage{
		Schema: consts.DataBaseDSH,
		Table:  consts.TableTenantLLM,
		Data:   g.Map{"tenant_id": tenantId, "llm_name": llmName},
	}

	err = s.sendMessage(
		ctx,
		consts.ActionInsert,
		gconv.Bytes(mqMessage.String()),
		consts.RouteKeyPayloadChanged,
		fmt.Sprintf("tenant_id:%s", tenantId),
	)

	// Log and return the error if it occurred
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return gerror.WrapCode(consts.ErrorGeneral, err)
	}

	// Update the cache with the new LLM of the tenant
	s.setLLMTenantToCache(ctx, tenantId, llmName)

	return
}

// RemoveTenantLLM is a method of the sDataStore struct.
// It removes the specified Language Learning Models (LLMs) from a tenant in the database and updates the cache.
//
// The method first logs the tenant ID and the LLM names.
// It then starts a transaction and attempts to delete the specified LLMs of the tenant from the database.
// If an error occurs during this operation, it logs the error and returns it.
// If the operation is successful, it updates the cache by removing the specified LLMs of the tenant.
//
// Parameters:
// ctx : context.Context - The context in which the method is called.
// tenantId : string - The ID of the tenant from which the LLMs should be removed.
// llms : []string - The names of the LLMs to be removed from the tenant.
//
// Returns:
// err : error - The error that occurred during the execution of the method, if any.
func (s *sDataStore) RemoveTenantLLM(ctx context.Context, tenantId string, llms []string) (err error) {
	// Log the tenant ID and the LLM names
	g.Log().Cat(consts.INFO).Infof(ctx, "Remove Tenant LLM : tenant id %v llms %v", tenantId, llms)
	if len(llms) == 0 {
		return gerror.NewCode(consts.ErrorGeneral, "LLM not exist")
	}

	// 使用IN条件而不是多个OR条件
	whereCond := "tenant_id = ? AND llm_name IN (?)"
	params := g.Slice{tenantId, llms}

	mqMessage := &model.MQMessage{
		Schema:          consts.DataBaseDSH,
		Table:           consts.TableTenantLLM,
		WhereConditions: whereCond,
		WhereParams:     params,
	}
	err = s.sendMessage(
		ctx,
		consts.ActionDelete,
		gconv.Bytes(mqMessage.String()),
		consts.RouteKeyPayloadChanged,
		fmt.Sprintf("tenant_id:%s", tenantId),
	)

	// Log and return the error if it occurred
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
	} else {
		// Update the cache by removing the specified LLMs of the tenant
		remLLM := gset.NewStrSetFrom(llms)
		v, _ := g.Redis().Get(ctx, fmt.Sprintf(consts.TenantLLMKeyFormat, tenantId))
		if !v.IsEmpty() {
			t := garray.NewStrArrayFrom(v.Strings())
			t.RemoveValues(remLLM.Slice()...)
			_ = g.Redis().SetEX(ctx, fmt.Sprintf(
				consts.TenantLLMKeyFormat,
				tenantId,
			), t.Slice(), gconv.Int64(gtime.D.Seconds()))
		}

	}

	return
}

func (s *sDataStore) GetLLMs(ctx context.Context, detail bool) (ret *gjson.Json) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Get LLMS detail: %v", detail)
	keys, _ := g.Redis().Keys(ctx, consts.LLMParamsKeyPattern)
	ret = gjson.New(nil)
	llms := garray.New()
	fnFillLLMs := func(llm *model.LLMConfiguration) {
		if !detail {
			llms.Append(g.Map{
				"llm_name":    llm.LLMName,
				"description": llm.Description,
			})
		} else {
			llms.Append(llm)
		}
	}

	if len(keys) > 0 {
		for _, key := range keys {
			v, _ := g.Redis().Get(ctx, key)
			if !v.IsNil() {
				llm := &model.LLMConfiguration{}
				_ = v.Scan(llm)
				fnFillLLMs(llm)
			}
		}
	} else {

		req := model.GetContentsReq{
			Schema: consts.DataBaseDSH,
			Table:  consts.TableLLMParams,
		}
		res, err := s.getContents(ctx, req)
		if err != nil {
			return ret
		}

		for _, record := range res.Contents {
			var llm *model.LLMConfiguration
			_ = gconv.Struct(record, &llm)
			fnFillLLMs(llm)
		}

	}

	_ = ret.Set("data", llms.Slice())

	return
}

func (s *sDataStore) GetTenantLLM(ctx context.Context, tenantId string) (ret *gjson.Json) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Get Tenant LLM : tenant id %v", tenantId)
	ret = gjson.New(nil)
	llms := garray.New()
	// firstly find llm params from cache
	if isExist, _ := g.Redis().Exists(ctx, fmt.Sprintf(consts.TenantLLMKeyFormat, tenantId)); isExist == 1 {
		llmNames, _ := g.Redis().Get(ctx, fmt.Sprintf(consts.TenantLLMKeyFormat, tenantId))
		for _, llmName := range llmNames.Strings() {
			if isExist, _ = g.Redis().Exists(ctx, fmt.Sprintf(consts.LLMParamsKeyFormat, llmName)); isExist == 1 {
				llmParams, _ := g.Redis().Get(ctx, fmt.Sprintf(consts.LLMParamsKeyFormat, llmName))
				llm := &model.LLMConfiguration{}
				_ = llmParams.Scan(llm)
				llms.Append(llm)
			} else {
				// if not found in cache, find from db
				llm := s.getLLMParamsByName(ctx, llmName)
				llms.Append(llm)
				if llm != nil {
					// set to cache
					_, _ = g.Redis().Set(ctx, fmt.Sprintf(consts.LLMParamsKeyFormat, llmName), llm)
				}
			}
		}

	} else {

		// if not found in cache, find from db
		req := model.GetContentsReq{
			Schema:    consts.DataBaseDSH,
			Table:     consts.TableTenantLLM,
			WhereCond: "tenant_id = ?",
			Params:    g.Slice{tenantId},
			Fields:    g.Slice{"llm_name"},
		}
		res, err := s.getContents(ctx, req)
		if err != nil {
			g.Log().Cat(consts.ERROR).Error(ctx, err)
		} else {
			llmNames := make([]string, 0)
			for _, rec := range res.Contents {
				llmName := gconv.String(rec["llm_name"])
				llm := s.getLLMParamsByName(ctx, llmName)
				if llm != nil {
					llms.Append(llm)
					llmNames = append(llmNames, llm.LLMName)
				}
			}
			// set to cache
			_ = g.Redis().SetEX(ctx, fmt.Sprintf(consts.TenantLLMKeyFormat, tenantId), llmNames, gconv.Int64(gtime.D.Seconds()))
		}

	}
	_ = ret.Set("data", llms.Slice())

	return
}

func (s *sDataStore) GetLLMTenantsList(ctx context.Context) []*gjson.Json {
	ret := make([]*gjson.Json, 0)
	g.Log().Cat(consts.INFO).Info(ctx, "Get LLMTenantsList")
	keys, _ := g.Redis().Keys(ctx, consts.TenantLLMKeyPattern)
	for _, key := range keys {
		v, _ := g.Redis().Get(ctx, key)
		if !v.IsEmpty() {
			t := gstr.Split(key, ":")
			if len(t) >= 2 {
				for _, llmName := range v.Strings() {
					j := gjson.New(nil)
					_ = j.Set("llm_name", llmName)
					_ = j.Set("tenant_id", t[1])
					ret = append(ret, j)
				}
			}
		}
	}
	if len(ret) == 0 {

		req := model.GetContentsReq{
			Schema: consts.DataBaseDSH,
			Table:  consts.TableTenantLLM,
			Fields: g.Slice{"llm_name", "tenant_id"},
		}
		res, err := s.getContents(ctx, req)
		if err != nil {
			g.Log().Cat(consts.ERROR).Error(ctx, err)
			return ret
		}

		for _, record := range res.Contents {
			llmName := gconv.String(record["llm_name"])
			tenantId := gconv.String(record["tenant_id"])
			j := gjson.New(nil)
			_ = j.Set("llm_name", llmName)
			_ = j.Set("tenant_id", tenantId)
			ret = append(ret, j)
		}
	}

	return ret
}

func (s *sDataStore) SetTenantParams(ctx context.Context, tenantId, params string) (err error) {
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "Set tenant params , tenant id :%v ,params:%v", tenantId, params)
	redKey := fmt.Sprintf(consts.TenantParamsKeyFormat, tenantId)
	_, _ = g.Redis().Set(ctx, redKey, params)

	mqMessage := &model.MQMessage{
		Schema: consts.DataBaseDSH,
		Table:  consts.TableTenantParams,
		Data: g.Map{
			"tenant_id": tenantId,
			"params":    params,
		},
	}

	err = s.sendMessage(
		ctx,
		consts.ActionInsert,
		gconv.Bytes(mqMessage.String()),
	)

	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		err = gerror.WrapCode(consts.ErrorGeneral, err)
		return
	}

	return
}

func (s *sDataStore) GetTenantParams(ctx context.Context, tenantId string) (params string, err error) {
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "Get tenant params , tenant id :%v  ", tenantId)
	redKey := fmt.Sprintf(consts.TenantParamsKeyFormat, tenantId)
	if isExist, _ := g.Redis().Exists(ctx, redKey); isExist == 1 {
		v, e := g.Redis().Get(ctx, redKey)
		if e == nil {
			params = v.String()
			return
		}
	}

	req := model.GetContentsReq{
		Schema:    consts.DataBaseDSH,
		Table:     consts.TableTenantParams,
		WhereCond: "tenant_id = ?",
		Params:    g.Slice{tenantId},
		Fields:    g.Slice{"params"},
	}
	res, err := s.getContents(ctx, req)
	if err != nil {
		return "", err
	}
	if len(res.Contents) > 0 {
		params = gconv.String(res.Contents[0]["params"])
	}

	return
}
func (s *sDataStore) getLLMParamsByName(ctx context.Context, llmName string) *model.LLMConfiguration {
	var ret *model.LLMConfiguration
	req := model.GetContentsReq{
		Schema:    consts.DataBaseDSH,
		Table:     consts.TableLLMParams,
		WhereCond: "llm_name = ?",
		Params:    g.Slice{llmName},
	}
	res, err := s.getContents(ctx, req)
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return nil
	}
	if len(res.Contents) > 0 {
		_ = gconv.Struct(res.Contents[0], &ret)
	}
	return ret
}

// setLLMTenantToCache is a method of the sDataStore struct.
// It sets the LLM (Language Learning Model) of a tenant to the cache.
//
// The method first checks if a cache object with a key formatted as TenantLLMKeyFormat and the tenant_id as the value exists.
// If such a cache object exists, it retrieves the value of the cache object and checks if it contains the llm_name.
// If the llm_name is not contained in the value of the cache object, it appends the llm_name to the value and sets the cache object with the new value.
// The cache object is set to expire after a duration of one day.
// If no cache object with a key formatted as TenantLLMKeyFormat and the tenant_id as the value exists, it sets a new cache object with a key formatted as TenantLLMKeyFormat and the tenant_id as the value, and a value of a slice containing the llm_name.
// The new cache object is set to expire after a duration of one day.
//
// Parameters:
// ctx : context.Context - The context in which the method is called.
// tenantId : string - The ID of the tenant whose LLM should be set to the cache.
// llmName : string - The name of the LLM to be set to the cache.
func (s *sDataStore) setLLMTenantToCache(ctx context.Context, tenantId string, llmName string) {
	if isExist, _ := g.Redis().Exists(ctx, fmt.Sprintf(consts.TenantLLMKeyFormat, tenantId)); isExist == 1 {
		v, _ := g.Redis().Get(ctx, fmt.Sprintf(consts.TenantLLMKeyFormat, tenantId))
		if !garray.NewStrArrayFrom(v.Strings()).Contains(llmName) {
			newV := append(v.Strings(), llmName)
			_ = g.Redis().SetEX(ctx, fmt.Sprintf(consts.TenantLLMKeyFormat, tenantId), newV, gconv.Int64(gtime.D.Seconds()))
		}

	} else {
		_ = g.Redis().SetEX(ctx, fmt.Sprintf(consts.TenantLLMKeyFormat, tenantId), []string{llmName}, gconv.Int64(gtime.D.Seconds()))
	}
}
