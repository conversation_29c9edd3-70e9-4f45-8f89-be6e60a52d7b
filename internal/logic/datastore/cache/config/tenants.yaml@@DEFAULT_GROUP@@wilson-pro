server:
 address: "*************:8084"
 swaggerPath: "/swagger"
 openapiPath: "/api.json" 
logger:
  path: "./logs"
  file:  "tenants_{Y-m-d}.log"
  rotateSize: "10M"
  rotateBackupLimit: 3 
  rotateBackupExpire: "3d"
  rotateBackupCompress: 9
  level: "dev"
  stdout: true
weaviate:
 host: ************:8070
 scheme: http
 recreate_class_onstart: false
system:
 keep_cache_duration: "1d"
 access_token_expire: "1d"
 refresh_token_expire: "7d"
 issuer: "Ai3"
 secret_key: "seckey"
 admin_account: "admin"
 admin_pass: "a~1d@3m$"
 update_services_duration: "1m"
services:
  -  service_name: "aile-gateway"
     key: "c0e2a635-a820-4b0b-ad75-827d1dfd543a"
     include:
       -  micro_service: "quizto"
          actions: ["chat"]
  
permissions:
  exclude:
   quizto: ["deleteall","get_tenants"]
redis:
  default:
    address: ************:6379
    db: 0
database:
 default:
  link: "mariadb:root:jeed77066188@tcp(*************:3306)"
  debug: true
llms:
 - llm_name: "QbiBotOpenAI"
   description: "AOAI of Qbibot"
   base_url: "https://qbibotopenai.openai.azure.com/"
   token: "050928bf5c1b47e5b3a2da43b164f383"
   resource_name: "qbibotopenai"
   embedding_model: "text-embedding-ada-002"
   model: "gpt-35-turbo"
   api_version: "2023-12-01-preview"
   temperature: 0.5
   max_token: 4096
llm:
  check_llms_interval: "1m"
  token: "050928bf5c1b47e5b3a2da43b164f383"
  resourceName: "qbibotopenai"
  deploymentId: "text-embedding-ada-002"



