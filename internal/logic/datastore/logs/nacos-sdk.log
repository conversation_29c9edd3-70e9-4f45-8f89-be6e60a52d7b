2024-04-10T10:13:15.282+0800	[34mINFO[0m	clients/client_factory.go:47	logDir:<./logs>   cacheDir:<./cache>
2024-04-10T10:13:15.784+0800	[34mINFO[0m	config_client/config_proxy.go:180	[client.ListenConfig] request params:map[Listening-Configs:tenants.yamlDEFAULT_GROUPwilson-pro tenant:wilson-pro] header:map[Content-Type:application/x-www-form-urlencoded;charset=utf-8 Long-Pulling-Timeout:30000 Long-Pulling-Timeout-No-Hangup:true accessKey: secretKey:] 

2024-04-10T10:13:15.937+0800	[34mINFO[0m	config_client/config_client.go:435	[client.ListenConfig] config changed:tenants.yaml%02DEFAULT_GROUP%02wilson-pro%01

2024-04-10T10:13:16.111+0800	[34mINFO[0m	config_client/config_proxy.go:180	[client.ListenConfig] request params:map[Listening-Configs:tenants.yamlDEFAULT_GROUPa6e81695e29d84e2232877a30da99898wilson-pro tenant:wilson-pro] header:map[Content-Type:application/x-www-form-urlencoded;charset=utf-8 Long-Pulling-Timeout:30000 accessKey: secretKey:] 

2024-04-10T10:13:50.590+0800	[34mINFO[0m	config_client/config_client.go:433	[client.ListenConfig] no change
2024-04-10T10:13:52.648+0800	[34mINFO[0m	config_client/config_proxy.go:180	[client.ListenConfig] request params:map[Listening-Configs:tenants.yamlDEFAULT_GROUPa6e81695e29d84e2232877a30da99898wilson-pro tenant:wilson-pro] header:map[Content-Type:application/x-www-form-urlencoded;charset=utf-8 Long-Pulling-Timeout:30000 accessKey: secretKey:] 

2024-04-10T10:14:24.617+0800	[34mINFO[0m	config_client/config_client.go:433	[client.ListenConfig] no change
2024-04-10T10:14:31.529+0800	[34mINFO[0m	config_client/config_proxy.go:180	[client.ListenConfig] request params:map[Listening-Configs:tenants.yamlDEFAULT_GROUPa6e81695e29d84e2232877a30da99898wilson-pro tenant:wilson-pro] header:map[Content-Type:application/x-www-form-urlencoded;charset=utf-8 Long-Pulling-Timeout:30000 accessKey: secretKey:] 

 random server.
2024-04-10T10:17:08.134+0800	[34mINFO[0m	clients/client_factory.go:47	logDir:<./logs>   cacheDir:<./cache>
2024-04-10T10:17:08.384+0800	ERROR	cache/disk_cache.go:53	read cacheDir:./cache/naming/wilson-pro failed!err:open ./cache/naming/wilson-pro: no such file or directory
2024-04-10T10:17:08.634+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55454
2024-04-10T10:17:08.634+0800	INFO	rpc/rpc_client.go:236	[RpcClient.Start] 8c26b065-a9ab-48ce-bae7-23ae8cecf51e try to connect to server on start up, server: {serverIp:dev.zhucl1006.top serverPort:8848 serverGrpcPort:9848}
2024-04-10T10:17:08.637+0800	INFO	util/common.go:96	Local IP:************
2024-04-10T10:17:08.637+0800	[34mINFO[0m	config_client/config_proxy.go:180	[client.ListenConfig] request params:map[Listening-Configs:tenants.yamlDEFAULT_GROUPa6e81695e29d84e2232877a30da99898wilson-pro tenant:wilson-pro] header:map[Content-Type:application/x-www-form-urlencoded;charset=utf-8 Long-Pulling-Timeout:30000 Long-Pulling-Timeout-No-Hangup:true accessKey: secretKey:] 

2024-04-10T10:17:08.802+0800	[34mINFO[0m	config_client/config_client.go:433	[client.ListenConfig] no change
2024-04-10T10:17:08.813+0800	[34mINFO[0m	config_client/config_proxy.go:180	[client.ListenConfig] request params:map[Listening-Configs:tenants.yamlDEFAULT_GROUPa6e81695e29d84e2232877a30da99898wilson-pro tenant:wilson-pro] header:map[Content-Type:application/x-www-form-urlencoded;charset=utf-8 Long-Pulling-Timeout:30000 accessKey: secretKey:] 

2024-04-10T10:17:08.970+0800	INFO	rpc/rpc_client.go:246	8c26b065-a9ab-48ce-bae7-23ae8cecf51e success to connect to server {serverIp:dev.zhucl1006.top serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1712715428891_223.68.134.166_5592
2024-04-10T10:19:02.160+0800	[34mINFO[0m	config_client/config_client.go:433	[client.ListenConfig] no change
2024-04-10T10:19:11.501+0800	[34mINFO[0m	clients/client_factory.go:47	logDir:<./logs>   cacheDir:<./cache>
2024-04-10T10:19:11.771+0800	ERROR	cache/disk_cache.go:53	read cacheDir:./cache/naming/wilson-pro failed!err:open ./cache/naming/wilson-pro: no such file or directory
2024-04-10T10:19:12.005+0800	[34mINFO[0m	config_client/config_proxy.go:180	[client.ListenConfig] request params:map[Listening-Configs:tenants.yamlDEFAULT_GROUPa6e81695e29d84e2232877a30da99898wilson-pro tenant:wilson-pro] header:map[Content-Type:application/x-www-form-urlencoded;charset=utf-8 Long-Pulling-Timeout:30000 Long-Pulling-Timeout-No-Hangup:true accessKey: secretKey:] 

2024-04-10T10:19:12.033+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55813
2024-04-10T10:19:12.034+0800	INFO	rpc/rpc_client.go:236	[RpcClient.Start] a51b8b8b-d037-4977-98ab-962b1f2b5d6c try to connect to server on start up, server: {serverIp:dev.zhucl1006.top serverPort:8848 serverGrpcPort:9848}
2024-04-10T10:19:12.036+0800	INFO	util/common.go:96	Local IP:************
2024-04-10T10:19:12.186+0800	[34mINFO[0m	config_client/config_client.go:433	[client.ListenConfig] no change
2024-04-10T10:19:12.197+0800	[34mINFO[0m	config_client/config_proxy.go:180	[client.ListenConfig] request params:map[Listening-Configs:tenants.yamlDEFAULT_GROUPa6e81695e29d84e2232877a30da99898wilson-pro tenant:wilson-pro] header:map[Content-Type:application/x-www-form-urlencoded;charset=utf-8 Long-Pulling-Timeout:30000 accessKey: secretKey:] 

2024-04-10T10:19:12.367+0800	INFO	rpc/rpc_client.go:246	a51b8b8b-d037-4977-98ab-962b1f2b5d6c success to connect to server {serverIp:dev.zhucl1006.top serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1712715552288_223.68.134.166_5593
2024-04-10T10:20:26.317+0800	[31mERROR[0m	nacos_server/nacos_server.go:199	api</v1/cs/configs/listener>,method:<POST>, params:<{"Listening-Configs":"tenants.yaml\u0002DEFAULT_GROUP\u0002a6e81695e29d84e2232877a30da99898\u0002wilson-pro\u0001","accessToken":"eyJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJuYWNvcyIsImV4cCI6MTcxMjczMzU1MX0.jwnEvX1qyikNVlR0t-Zh-cF7qYdwTE6U61G9U4fuCn8","tenant":"wilson-pro"}>, call domain error:<Post "http://dev.zhucl1006.top:8848/nacos/v1/cs/configs/listener": context deadline exceeded (Client.Timeout exceeded while awaiting headers)> , result:<>
2024-04-10T10:20:49.677+0800	ERROR	rpc/grpc_client.go:193	connectionId 1712715552288_223.68.134.166_5593 request stream error, switch server, error=rpc error: code = Unavailable desc = keepalive ping failed to receive ACK within timeout
2024-04-10T10:20:49.677+0800	ERROR	rpc/rpc_client.go:445	client sendHealthCheck failed,err=rpc error: code = DeadlineExceeded desc = context deadline exceeded
2024-04-10T10:20:49.677+0800	INFO	rpc/rpc_client.go:430	a51b8b8b-d037-4977-98ab-962b1f2b5d6c server healthy check fail, currentConnection=1712715552288_223.68.134.166_5593
2024-04-10T10:20:49.677+0800	INFO	rpc/rpc_client.go:339	a51b8b8b-d037-4977-98ab-962b1f2b5d6c try to re connect to a new server, server is not appointed, will choose a random server.
2024-04-10T10:21:37.186+0800	[34mINFO[0m	clients/client_factory.go:47	logDir:<./logs>   cacheDir:<./cache>
2024-04-10T10:21:37.428+0800	ERROR	cache/disk_cache.go:53	read cacheDir:./cache/naming/wilson-pro failed!err:open ./cache/naming/wilson-pro: no such file or directory
2024-04-10T10:21:37.690+0800	[34mINFO[0m	config_client/config_proxy.go:180	[client.ListenConfig] request params:map[Listening-Configs:tenants.yamlDEFAULT_GROUPa6e81695e29d84e2232877a30da99898wilson-pro tenant:wilson-pro] header:map[Content-Type:application/x-www-form-urlencoded;charset=utf-8 Long-Pulling-Timeout:30000 Long-Pulling-Timeout-No-Hangup:true accessKey: secretKey:] 

2024-04-10T10:21:37.690+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55213
2024-04-10T10:21:37.691+0800	INFO	rpc/rpc_client.go:236	[RpcClient.Start] 880c9b0e-ece2-40a2-88de-f50d03df786d try to connect to server on start up, server: {serverIp:dev.zhucl1006.top serverPort:8848 serverGrpcPort:9848}
2024-04-10T10:21:37.694+0800	INFO	util/common.go:96	Local IP:************
2024-04-10T10:21:37.843+0800	[34mINFO[0m	config_client/config_client.go:433	[client.ListenConfig] no change
2024-04-10T10:21:37.854+0800	[34mINFO[0m	config_client/config_proxy.go:180	[client.ListenConfig] request params:map[Listening-Configs:tenants.yamlDEFAULT_GROUPa6e81695e29d84e2232877a30da99898wilson-pro tenant:wilson-pro] header:map[Content-Type:application/x-www-form-urlencoded;charset=utf-8 Long-Pulling-Timeout:30000 accessKey: secretKey:] 

2024-04-10T10:21:38.023+0800	INFO	rpc/rpc_client.go:246	880c9b0e-ece2-40a2-88de-f50d03df786d success to connect to server {serverIp:dev.zhucl1006.top serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1712715697941_223.68.134.166_5594
2024-04-10T10:21:48.566+0800	[34mINFO[0m	clients/client_factory.go:47	logDir:<./logs>   cacheDir:<./cache>
2024-04-10T10:21:48.822+0800	ERROR	cache/disk_cache.go:53	read cacheDir:./cache/naming/wilson-pro failed!err:open ./cache/naming/wilson-pro: no such file or directory
2024-04-10T10:21:49.070+0800	[34mINFO[0m	config_client/config_proxy.go:180	[client.ListenConfig] request params:map[Listening-Configs:tenants.yamlDEFAULT_GROUPa6e81695e29d84e2232877a30da99898wilson-pro tenant:wilson-pro] header:map[Content-Type:application/x-www-form-urlencoded;charset=utf-8 Long-Pulling-Timeout:30000 Long-Pulling-Timeout-No-Hangup:true accessKey: secretKey:] 

2024-04-10T10:21:49.078+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55756
2024-04-10T10:21:49.078+0800	INFO	rpc/rpc_client.go:236	[RpcClient.Start] 1b24feba-3ae9-4bbd-8da7-47ca634b6b10 try to connect to server on start up, server: {serverIp:dev.zhucl1006.top serverPort:8848 serverGrpcPort:9848}
2024-04-10T10:21:49.081+0800	INFO	util/common.go:96	Local IP:************
2024-04-10T10:21:49.229+0800	[34mINFO[0m	config_client/config_client.go:433	[client.ListenConfig] no change
2024-04-10T10:21:49.239+0800	[34mINFO[0m	config_client/config_proxy.go:180	[client.ListenConfig] request params:map[Listening-Configs:tenants.yamlDEFAULT_GROUPa6e81695e29d84e2232877a30da99898wilson-pro tenant:wilson-pro] header:map[Content-Type:application/x-www-form-urlencoded;charset=utf-8 Long-Pulling-Timeout:30000 accessKey: secretKey:] 

2024-04-10T10:21:49.400+0800	INFO	rpc/rpc_client.go:246	1b24feba-3ae9-4bbd-8da7-47ca634b6b10 success to connect to server {serverIp:dev.zhucl1006.top serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1712715709322_223.68.134.166_5595
