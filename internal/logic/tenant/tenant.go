package tenant

import (
	"context"
	"fmt"
	"github.com/gogf/gf/v2/net/gsvc"
	"github.com/gogf/gf/v2/os/gctx"
	"tenants/boot"
	"tenants/internal/consts"
	"tenants/internal/model"
	"tenants/internal/service"
	"time"

	"github.com/gogf/gf/v2/container/garray"
	"github.com/gogf/gf/v2/container/gmap"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/gclient"
	"github.com/gogf/gf/v2/os/gcache"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
)

type sTenant struct {
	dataCache *gcache.Cache
	client    *gclient.Client
}

func init() {
	service.RegisterTenant(New())

}

func New() service.ITenant {
	boot.WaitReady()
	s := &sTenant{
		dataCache: gcache.New(),
		client:    g.Client(),
	}
	s.client.SetDiscovery(gsvc.GetRegistry())
	ctx := gctx.GetInitCtx()
	if _, err := s.ListTenants(ctx); err != nil {

		g.Log().Cat(consts.DEBUG).Debug(ctx, err)
	}

	return s
}

func (n *sTenant) setTenantInfoInCache(ctx context.Context, ti *model.TenantInfo) (err error) {
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "%+v", ti)
	err = n.dataCache.Set(ctx, ti.TenantID, ti, 0)

	return
}

func (n *sTenant) removeFromCache(ctx context.Context, tenantID string) (err error) {
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "tenantID %v", tenantID)
	isExist, err := n.checkTenantInfoInCache(ctx, tenantID)
	if err != nil {
		return err
	}
	if isExist {
		_, err = n.dataCache.Remove(ctx, tenantID)
	}
	return
}

func (n *sTenant) GetTenantAvailable(ctx context.Context, tenantID string) (available bool, err error) {
	ti, _ := n.getTenantInfoByIDFromCache(ctx, tenantID)
	if ti != nil {
		available = ti.Available
		return
	} else {
		exist, dataSet, err := n.tenantIsExisted(ctx, tenantID)
		if err != nil {
			return false, err
		}
		if !exist {
			g.Log().Cat(consts.DEBUG).Debugf(ctx, "tenantID %v not exist", tenantID)
			err = gerror.NewCode(consts.ErrorTenantNotExist)
			return false, err
		}

		if dataSet.Contains("Get.Tenants.0.available") {
			available = dataSet.Get("Get.Tenants.0.available").Bool()
		} else {
			g.Log().Cat(consts.DEBUG).Debugf(ctx, "tenantID  %v available key is not exist", tenantID)
			err = gerror.NewCode(consts.ErrorGeneral)
			return false, err
		}

	}
	return
}

func (n *sTenant) getTenantInfoByIDFromCache(ctx context.Context, tenantID string) (ti *model.TenantInfo, err error) {
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "tenantID %v", tenantID)
	isExist, err := n.checkTenantInfoInCache(ctx, tenantID)
	if err != nil {
		return nil, err
	}
	if isExist {
		var vData *g.Var
		vData, err = n.dataCache.Get(ctx, tenantID)
		if err != nil {
			return
		}
		err = vData.Scan(&ti)
	} else {
		err = gerror.Newf("tenantID :  %s not in cache", tenantID)
	}

	return
}

func (n *sTenant) getAllTenantsInfoFromCache(ctx context.Context) (ary *garray.Array, err error) {
	g.Log().Cat(consts.DEBUG).Debug(ctx, "Get all tenant info from cache")
	values, err := n.dataCache.Values(ctx)
	if err != nil {
		return nil, err
	}
	ary = garray.NewFrom(values)

	return
}

func (n *sTenant) checkTenantInfoInCache(ctx context.Context, tenantID string) (isExist bool, err error) {
	return n.dataCache.Contains(ctx, tenantID)
}

func (n *sTenant) checkToken(ctx context.Context, claims *model.Claims) (isValid bool, err error) {
	// 过期时间在当前时间之后，表示有效
	isValid = gtime.New(claims.ExpiresAt).After(gtime.Now())
	return
}

func (n *sTenant) parseToken(ctx context.Context, token string) (claims *model.Claims, err error) {
	vSecKey, _ := g.Cfg().Get(ctx, "system.secret_key", "seckey")
	tokenClaims, err := jwt.ParseWithClaims(token, &model.Claims{}, func(token *jwt.Token) (interface{}, error) {
		return vSecKey.Bytes(), nil
	})
	if err != nil {
		return
	}

	ok := false
	if claims, ok = tokenClaims.Claims.(*model.Claims); ok && tokenClaims.Valid {
		return
	}
	return nil, err
}

func (n *sTenant) createTokens(ctx context.Context, tenantID, password, appkey string, tokenType int) (token string, err error) {
	vAccessTokenExpire, _ := g.Cfg().Get(ctx, "system.access_token_expire", "1d")
	vRefreshTokenExpire, _ := g.Cfg().Get(ctx, "system.refresh_token_expire", "7d")
	vIssuser, _ := g.Cfg().Get(ctx, "system.issuer", "Ai3")
	vSecKey, _ := g.Cfg().Get(ctx, "system.secret_key", "seckey")
	var expireAT int64
	if tokenType == consts.GenAccessToken {
		expireAT = gtime.Now().Add(vAccessTokenExpire.Duration()).Unix()
	} else if tokenType == consts.GenRefreshToken {
		expireAT = gtime.Now().Add(vRefreshTokenExpire.Duration()).Unix()
	}

	claims := model.Claims{
		TenantID: tenantID,
		Password: password,
		AppKey:   appkey,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Unix(expireAT, 0)),
			Issuer:    vIssuser.String(),
		},
	}

	token, err = jwt.NewWithClaims(jwt.SigningMethodHS256, claims).SignedString(vSecKey.Bytes())
	if err != nil {
		return
	}
	return
}

/*
Function Name: NewTokens
Description: Generate new access and refresh tokens for a given tenant using the provided credentials.
Input Parameters:
   - ctx: Context for the request.
   - tenantID: The ID of the tenant for which tokens are requested.
   - password: The tenant's password.
   - appKey: The tenant's application key.
Output:
   - accessToken: The newly generated access token.
   - refreshToken: The newly generated refresh token.
   - err: An error if the operation encounters an issue, otherwise nil.
Exception:
   - If an error occurs during token generation, an error is returned, and the error message contains specific details.
   - The function attempts to retrieve tenant information from a cache. If successful, the provided password and appKey are compared to cached values. If they match, new tokens are generated. If not, a database check is performed to validate the password and appKey, and tokens are generated accordingly.
   - If the tenant does not exist in the database, an error with the code "ErrorTenantNotExist" is returned.
   - If the provided appKey does not match the one stored in the database, an error with the code "ErrorAppKey" is returned.
*/

func (n *sTenant) NewTokens(ctx context.Context, tenantID, password, appKey string) (accessToken, refreshToken string, err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Request new tokens  ,tenantID:%s ...  ", tenantID)
	fnGenTokens := func() (string, string, error) {
		a, e := n.createTokens(ctx, tenantID, password, appKey, consts.GenAccessToken)
		if e != nil {
			return "", "", e
		}
		r, e := n.createTokens(ctx, tenantID, password, appKey, consts.GenRefreshToken)
		if e != nil {
			return "", "", e
		}
		return a, r, nil
	}

	// 先从缓存中查找
	var ti *model.TenantInfo
	ti, err = n.getTenantInfoByIDFromCache(ctx, tenantID)
	if err == nil && ti != nil {
		if ti.Password == password && ti.APPKey == appKey && ti.Available {
			// create token
			return fnGenTokens()
		}
	}
	// 从db中查找
	isExist, _, err := n.tenantIsExisted(ctx, tenantID, true)
	if err != nil {
		return
	}
	if !isExist {
		err = gerror.NewCodef(consts.ErrorTenantNotExist, "Tenant %s is not exist", tenantID)
		return
	}
	err, props := n.validateTenantPass(ctx, tenantID, password)
	if err != nil {
		return
	}

	jsProps := gjson.New(props)
	if jsProps.Get(consts.ProAppKey).String() == appKey {
		return fnGenTokens()
	} else {
		err = gerror.NewCode(consts.ErrorAppKey)
		return
	}
}
func (n *sTenant) sendMessage(ctx context.Context, action string, data []byte) (err error) {

	return service.MessageQ().Send(
		ctx,
		consts.RouteKeyMariaDB,
		action,
		data,
	)
}
func (n *sTenant) sendToDSH(ctx context.Context, in model.GetContentsReq) (out *model.GetContentsRes, err error) {
	vServiceName, _ := g.Cfg().Get(ctx, "system.data_service.name", "dsh.svc")
	vScheme, _ := g.Cfg().Get(ctx, "system.data_service.scheme", "http")

	url := fmt.Sprintf("%s://%s%s", vScheme.String(), vServiceName, consts.UriGetContent)
	response, err := n.client.ContentJson().SetHeader(consts.XHeaderService, consts.ServiceName).
		Post(ctx, url, in)
	if err != nil {
		return nil, err
	}
	defer response.Close()
	strResponse := response.ReadAllString()
	if !gjson.Valid(strResponse) {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Response: %v", strResponse)
		err = gerror.NewCode(consts.ErrorGeneral, "response is not valid")
		return
	}

	_ = gjson.New(strResponse).Scan(&out)

	return
}

// createClass creates the tenant class schema in Weaviate if it doesn't exist.
// It builds the class object with:
// - Name: Tenants
// - Description: Tenant information
// - Inverted index enabled for filtering
// - Properties:
//   - Tenant_Id: text
//   - OldPass: text
//   - App_Key: uuid
//   - Available: boolean
//
// The client is used to send the class creation request.
// An error is returned if the client is nil or request fails.
// This ensures the tenant class schema exists in Weaviate for data storage.
//func (n *sTenant) createClass(ctx context.Context) (err error) {
//	// todo  丟棄的代碼
//	if n.client != nil {
//		vResName, _ := g.Cfg().Get(ctx, "llm.resourceName", "")
//		vEmbMode, _ := g.Cfg().Get(ctx, "llm.deploymentId", "")
//
//		classObj := &models.Class{
//			Class:               consts.Tenants,
//			Description:         "Tenant's information",
//			InvertedIndexConfig: &models.InvertedIndexConfig{IndexNullState: true},
//			ModuleConfig: map[string]interface{}{
//				"text2vec-openai": map[string]interface{}{
//					"resourceName": vResName.String(),
//					"deploymentId": vEmbMode.String(),
//				},
//			},
//			Properties: consts.Properties,
//		}
//
//		err = n.client.Schema().ClassCreator().WithClass(classObj).Do(ctx)
//	} else {
//		err = gerror.New("weaviate client is nil ... ")
//	}
//
//	return
//}

// validateTenantPass validates tenant ID and password.
// It first checks if the tenant exists.
// If not, tenant not exist error is returned.
// The tenant object is fetched by ID.
// The password is extracted from properties.
// This is compared with the input password.
// On mismatch, invalid password error is returned.
// On match, success is returned.
// This provides password validation logic by
// comparing with data stored in Weaviate.
func (n *sTenant) validateTenantPass(ctx context.Context, tenantID, password string, checkOldPass ...bool) (err error, props map[string]interface{}) {
	// search from cache
	ti, err := n.getTenantInfoByIDFromCache(ctx, tenantID)
	bChkOldPass := true
	if len(checkOldPass) > 0 {
		bChkOldPass = checkOldPass[0]
	}

	if err != nil {
		g.Log().Cat(consts.DEBUG).Debug(ctx, err)
	} else {

		if bChkOldPass {
			if ti.Password == password {
				props = gconv.Map(ti)
			} else {
				err = gerror.NewCode(consts.ErrorPassword)
				return
			}
		} else {
			props = gconv.Map(ti)
		}

		return

	}

	out, err := n.sendToDSH(ctx, model.GetContentsReq{
		Schema:    consts.DataBaseDSH,
		Table:     consts.TableTenant,
		WhereCond: "tenant_Id = ? ",
		Params:    g.Slice{tenantID},
		Limit:     1,
	})

	if err != nil {
		err = gerror.WrapCode(consts.ErrorGeneral, err)
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}

	if len(out.Contents) == 0 {

		err = gerror.NewCode(consts.ErrorTenantNotExist)
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}
	if bChkOldPass {
		if gconv.String(out.Contents[0]["password"]) == password {
			props = out.Contents[0]
			return
		} else {
			err = gerror.NewCode(consts.ErrorPassword)

			return
		}

	} else {
		props = out.Contents[0]
	}
	return

}

// tenantIsExisted checks whether a tenant with the specified ID exists in the Weaviate system.
// Input parameters:
//   tenantID: string. The ID of the tenant to check for existence.
// Output parameters:
//   err: error. An error, if any, encountered during the process.
//   isExist: bool. Indicates whether the tenant exists (true) or not (false).
//   dataSet: *gjson.Json. The JSON data containing information about the tenant if it exists, nil otherwise.

func (n *sTenant) tenantIsExisted(ctx context.Context, tenantID string, status ...bool) (isExist bool, dataSet *gjson.Json, err error) {
	if n.client == nil {
		err = gerror.NewCode(consts.ErrorGeneral, "client is nil ... ")
		return
	}
	available := true
	if len(status) > 0 {
		available = status[0]
	}

	out, err := n.sendToDSH(ctx, model.GetContentsReq{
		Schema:    consts.DataBaseDSH,
		Table:     consts.TableTenant,
		WhereCond: "tenant_Id = ? and available = ? ",
		Params:    g.Slice{tenantID, available},
	})

	if err != nil {
		err = gerror.WrapCode(consts.ErrorGeneral, err)
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}
	if len(out.Contents) == 0 {
		err = gerror.NewCode(consts.ErrorTenantNotExist)
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}
	isExist = true
	dataSet = gjson.New(out.Contents[0])
	return

}

// createTenant creates a new tenant in Weaviate.
// It builds the data schema with properties:
// - Tenant_Id: The tenant ID
// - OldPass: The password
// - Available: Set to true
// The client is used to send a data creation request
// to the Tenants class with this schema.
// Any error from the request is returned.
// On success, the tenant is added to Weaviate storage.
// This allows persisting new tenants after validation.
func (n *sTenant) CreateTenant(ctx context.Context, tenantID, password string) (err error) {
	if n.client == nil {
		err = gerror.NewCode(consts.ErrorGeneral, "weaviate client is nil ... ")
		return
	}
	mqMessage := &model.MQMessage{
		Schema: consts.DataBaseDSH,
		Table:  consts.TableTenant,
		Data: g.Map{
			"tenant_Id": tenantID,
			"password":  password,
		},
	}

	err = n.sendMessage(ctx, consts.ActionInsert, []byte(mqMessage.String()))
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}

	// 将其放到缓存中保存
	_ = n.setTenantInfoInCache(ctx, &model.TenantInfo{
		TenantID:  tenantID,
		Password:  password,
		APPKey:    "",
		Available: true,
		Remark:    "",
	})

	return
}

// CheckOrCreateTenant 檢查租戶是否存在，如果不存在則創建
// 首先從緩存中查詢，如果不存在則查詢數據庫
// 如果數據庫中也不存在，則創建新租戶並創建分區表
func (n *sTenant) CheckOrCreateTenant(ctx context.Context, tenantID string) (err error) {
	if tenantID == "" {
		return gerror.NewCode(consts.ErrorGeneral, "tenant_id cannot be empty")
	}

	g.Log().Cat(consts.INFO).Infof(ctx, "Checking or creating tenant: %s", tenantID)

	// 1. 首先檢查本地緩存
	ti, err := n.getTenantInfoByIDFromCache(ctx, tenantID)
	if err == nil && ti != nil {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Tenant %s found in local cache", tenantID)
		// 同時檢查 Redis 緩存，如果不存在則設置
		n.setTenantInfoToRedis(ctx, ti)
		return nil
	}

	// 2. 檢查 Redis 緩存
	ti, err = n.getTenantInfoFromRedis(ctx, tenantID)
	if err == nil && ti != nil {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Tenant %s found in Redis cache", tenantID)
		// 設置到本地緩存
		_ = n.setTenantInfoInCache(ctx, ti)
		return nil
	}

	// 3. 查詢數據庫
	exist, _, err := n.tenantIsExisted(ctx, tenantID, true)
	if err != nil {
		// 如果錯誤是租戶不存在，則繼續創建流程
		if gerror.Code(err) != consts.ErrorTenantNotExist {
			g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to check tenant existence: %v", err)
			return err
		}
	}

	if exist {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Tenant %s found in database", tenantID)
		// 租戶存在，獲取完整信息並緩存
		props, err := n.getTenantProps(ctx, tenantID, "", false)
		if err != nil {
			g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to get tenant properties: %v", err)
			return err
		}

		ti = &model.TenantInfo{
			TenantID:  tenantID,
			Password:  gconv.String(props["password"]),
			APPKey:    gconv.String(props["app_Key"]),
			Available: gconv.Bool(props["available"]),
			Remark:    gconv.String(props["remark"]),
		}

		// 緩存到本地和 Redis
		_ = n.setTenantInfoInCache(ctx, ti)
		n.setTenantInfoToRedis(ctx, ti)
		return nil
	}

	// 4. 租戶不存在，創建新租戶
	g.Log().Cat(consts.INFO).Infof(ctx, "Tenant %s not found, creating new tenant", tenantID)

	// 使用 tenant_id 作為默認密碼
	defaultPassword := tenantID

	err = n.CreateTenant(ctx, tenantID, defaultPassword)
	if err != nil {
		g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to create tenant %s: %v", tenantID, err)
		return err
	}

	// 5. 創建分區表
	g.Log().Cat(consts.INFO).Infof(ctx, "Creating partition tables for tenant: %s", tenantID)
	if err := service.DataStore().CreatePartitionTablesForTenant(ctx, tenantID); err != nil {
		g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to create partition tables for tenant %s: %v", tenantID, err)
		// 分區表創建失敗不影響租戶創建結果，只記錄錯誤
	} else {
		g.Log().Cat(consts.INFO).Infof(ctx, "Successfully created partition tables for tenant: %s", tenantID)
	}

	g.Log().Cat(consts.INFO).Infof(ctx, "Successfully checked/created tenant: %s", tenantID)
	return nil
}

// setTenantInfoToRedis 將租戶信息設置到 Redis 緩存，過期時間為 1 天
func (n *sTenant) setTenantInfoToRedis(ctx context.Context, ti *model.TenantInfo) {
	if ti == nil {
		return
	}

	key := fmt.Sprintf(consts.TenantInfoKeyFormat, ti.TenantID)
	expireTime := gtime.D // 1 天過期時間

	err := g.Redis().SetEX(ctx, key, gjson.New(ti).MustToJsonString(), gconv.Int64(expireTime.Seconds()))
	if err != nil {
		g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to set tenant info to Redis for tenant %s: %v", ti.TenantID, err)
	} else {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Successfully cached tenant info to Redis for tenant: %s", ti.TenantID)
	}
}

// getTenantInfoFromRedis 從 Redis 緩存中獲取租戶信息
func (n *sTenant) getTenantInfoFromRedis(ctx context.Context, tenantID string) (ti *model.TenantInfo, err error) {
	key := fmt.Sprintf(consts.TenantInfoKeyFormat, tenantID)

	value, err := g.Redis().Get(ctx, key)
	if err != nil {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Failed to get tenant info from Redis for tenant %s: %v", tenantID, err)
		return nil, err
	}

	if value.IsNil() {
		return nil, gerror.Newf("tenant %s not found in Redis cache", tenantID)
	}

	ti = &model.TenantInfo{}
	err = value.Scan(ti)
	if err != nil {
		g.Log().Cat(consts.ERROR).Errorf(ctx, "Failed to parse tenant info from Redis for tenant %s: %v", tenantID, err)
		return nil, err
	}

	g.Log().Cat(consts.DEBUG).Debugf(ctx, "Successfully retrieved tenant info from Redis for tenant: %s", tenantID)
	return ti, nil
}

func (n *sTenant) deleteTenant(ctx context.Context, tenantID string) (err error) {
	mqMessage := &model.MQMessage{
		Schema:          consts.DataBaseDSH,
		Table:           consts.TableTenant,
		WhereConditions: "tenant_Id = ? ",
		WhereParams:     g.Slice{tenantID},
	}

	err = n.sendMessage(ctx, consts.ActionDelete, []byte(mqMessage.String()))

	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		err = gerror.WrapCode(consts.ErrorGeneral, err)
		return
	}

	return
}

// updateTenant updates an existing tenant in Weaviate.
// It first checks if the tenant exists using tenantIsExisted.
// If it doesn't exist, an error is returned.
// The record ID is retrieved from the check result.
// A data update request is made using the client to update the
// tenant record with the given properties.
// Any error from the request is returned.
// On success, the tenant is updated in Weaviate storage.
// This allows updating tenant information after validation.
func (n *sTenant) updateTenant(ctx context.Context, tenantID string, properties map[string]interface{}) (err error) {
	mqMessage := &model.MQMessage{
		Schema:          consts.DataBaseDSH,
		Table:           consts.TableTenant,
		WhereConditions: "tenant_Id = ?",
		WhereParams:     g.Slice{tenantID},
		Data:            properties,
	}

	err = n.sendMessage(ctx, consts.ActionUpdate, []byte(mqMessage.String()))

	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		err = gerror.NewCode(consts.ErrorGeneral)
	}

	ti, _ := n.getTenantInfoByIDFromCache(ctx, tenantID)
	if ti != nil {
		m := gconv.Map(ti)
		for k, v := range properties {
			m[k] = v
		}
		var newTi *model.TenantInfo
		_ = gconv.Scan(m, &newTi)

		if newTi != nil {
			_ = n.setTenantInfoInCache(ctx, newTi)
		}
	}

	return
}

func (n *sTenant) DisableTenant(ctx context.Context, tenantID string, marked bool) (err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Disable tenant %s  marked : %v... ", tenantID, marked)

	var (
		isExist = false
	)

	if marked {
		isExist, _, err = n.tenantIsExisted(ctx, tenantID, marked)
	} else {
		isExist, _, err = n.tenantIsExisted(ctx, tenantID)
	}

	if err != nil {
		return err
	}

	if !isExist {
		err = gerror.NewCode(consts.ErrorTenantNotExist)
		return
	}

	if marked {
		err = n.updateTenant(ctx, tenantID, g.Map{consts.ProAvailable: false})
	} else {
		err = n.deleteTenant(ctx, tenantID)
		if err != nil {
			g.Log().Cat(consts.ERROR).Error(ctx, err)
			return
		}
		// if marked is false, then remove it from the cache
		_ = n.removeFromCache(ctx, tenantID)
	}

	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
	}

	return
}

// ActivateTenant activates a tenant in Weaviate by setting Available to true.
// It first checks if the tenant exists and gets the object ID.
// A data update request is made using the client to update the
// tenant record with Available set to true.
// Any error from the request is returned.
// On success, the tenant is activated in Weaviate by having
// Available set to true.
// This provides the logic to activate a tenant account.
//
// Parameters:
// ctx: The context to use for the request.
// tenantID: The ID of the tenant to activate.
//
// Returns:
// err: Any error that occurred during the operation, otherwise nil.
func (n *sTenant) ActivateTenant(ctx context.Context, tenantID string) (err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Activate tenant %s ... ", tenantID)
	exist, _, err := n.tenantIsExisted(ctx, tenantID, false)
	if !exist {
		err = gerror.NewCode(consts.ErrorTenantNotExist)
		return
	}

	err = n.updateTenant(ctx, tenantID, g.Map{consts.ProAvailable: true})
	return
}

func (n *sTenant) Remark(ctx context.Context, tenantId, remark string) error {
	g.Log().Cat(consts.INFO).Infof(ctx, "Remark tenant %s and remark:%v ", tenantId, remark)

	exist, _, err := n.tenantIsExisted(ctx, tenantId, true)
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return err
	}

	if !exist {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Tenant %s is not exist", tenantId)
		return gerror.NewCode(consts.ErrorTenantNotExist)
	}

	return n.updateTenant(ctx, tenantId, g.Map{consts.ProRemark: remark})
}

// UpdateTenant updates the password for a tenant in Weaviate.
// It builds a property map with the new password.
// It calls updateTenant to update the tenant with this map.
// Any error is logged and returned.
// On success, the tenant's password is updated in Weaviate.
// This provides the logic to update a tenant's password.
func (n *sTenant) UpdateTenant(ctx context.Context, tenantID, oldPassword, newPassword string, isAdmin bool) (err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Update tenant %s with new password ... ", tenantID)
	if !isAdmin && g.IsEmpty(oldPassword) {
		g.Log().Cat(consts.DEBUG).Debug(ctx, "The old password is empty ")
		err = gerror.NewCode(consts.ErrorPassword)
		return
	}

	exist, _, err := n.tenantIsExisted(ctx, tenantID, true)
	if err != nil {
		return
	}
	if !exist {
		err = gerror.NewCodef(consts.ErrorTenantNotExist, "Tenant %s is not exist", tenantID)
		return
	}

	err, _ = n.validateTenantPass(ctx, tenantID, oldPassword, !isAdmin)

	if err != nil {
		return err
	}

	err = n.updateTenant(ctx, tenantID, g.Map{consts.ProPassword: newPassword})
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
	}

	return
}

// ListTenants retrieves all tenants from Weaviate.
// It uses pagination to fetch tenants from the objects API.
// A callback function is defined to parse each tenant object
// into a TenantInfo struct.
// Objects are fetched in batches using limit and after params.
// The next batch uses the last ID as the after param.
// This continues until no more objects are returned.
// All TenantInfo structs are added to an array.
// The array is converted to JSON and returned.
// Any errors from requests are returned.
// This provides the full logic to paginate and retrieve all
// tenants from Weaviate.
func (n *sTenant) ListTenants(ctx context.Context) (tenants *gjson.Json, err error) {
	g.Log().Cat(consts.INFO).Info(ctx, "List all tenants... ")
	// 先从缓存中查找
	ary, err := n.getAllTenantsInfoFromCache(ctx)
	if err == nil {
		if ary.Len() > 0 {
			tenants = gjson.New("tenants")
			ary.Iterator(func(k int, v interface{}) bool {
				_ = tenants.Append("tenants", v)
				return true
			})
			return

		}
	}

	if n.client == nil {
		err = gerror.NewCode(consts.ErrorGeneral, "Weaviate client is nil ... ")
		return
	}

	out, err := n.sendToDSH(ctx, model.GetContentsReq{
		Schema: consts.DataBaseDSH,
		Table:  consts.TableTenant,
	})
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}

	if len(out.Contents) == 0 {
		err = gerror.NewCode(consts.ErrorGeneral, "Tenant is empty ... ")
		return
	}
	tenants = gjson.New("tenants")

	fnTenants := func(props map[string]interface{}) {
		m := gmap.NewStrAnyMapFrom(props)
		if len(props) > 0 {

			t := &model.TenantInfo{
				TenantID:  m.GetVar(consts.ProTenantID).String(),
				Password:  m.GetVar(consts.ProPassword).String(),
				APPKey:    m.GetVar(consts.ProAppKey).String(),
				Available: m.GetVar(consts.ProAvailable).Bool(),
				Remark:    m.GetVar(consts.ProRemark).String(),
			}

			_ = tenants.Append("tenants", t)

			// 放入缓存 一份
			_ = n.setTenantInfoInCache(ctx, t)
		}
	}

	for _, content := range out.Contents {
		fnTenants(content)
	}
	return

}

// GenNewAppKeyForTenant generates a new app key for a tenant.
// It first checks if the tenant exists.
// A new GUID is generated as the app key.
// updateTenant is called to update the tenant with this new key.
// Any error is returned.
// On success, the new app key is returned.
// This provides the ability to rotate the app key for a tenant.

func (n *sTenant) GenNewAppKeyForTenant(ctx context.Context, tenantID, password string, isAdmin bool) (err error, newKey string) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Generate new app key for tenant %s ,is admin : %v", tenantID, isAdmin)
	if !isAdmin && g.IsEmpty(password) {
		g.Log().Cat(consts.DEBUG).Debug(ctx, "The password is empty")
		err = gerror.NewCode(consts.ErrorPassword)
		return
	}
	exist, _, err := n.tenantIsExisted(ctx, tenantID, true)
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}
	if !exist {
		err = gerror.NewCodef(consts.ErrorTenantNotExist, "Tenant %s is not exist", tenantID)
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}

	err, _ = n.validateTenantPass(ctx, tenantID, password, !isAdmin)
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return err, ""
	}

	appKey := uuid.New().String()

	err = n.updateTenant(
		ctx,
		tenantID,
		g.Map{consts.ProAppKey: appKey})
	if err != nil {
		return err, ""
	}

	newKey = appKey

	return
}

/*
Function Name: RefreshToken
Description: Refresh an access token using a provided refresh token.
Input Parameters:
   - ctx: Context for the request.
   - refreshToken: The refresh token used to obtain a new access token.
Output:
   - accessToken: The newly generated access token.
   - err: An error if the operation encounters an issue, otherwise nil.
Exception:
   - The function attempts to parse the provided refresh token. If parsing fails, an error is returned.
   - If the provided refresh token is expired or invalid, an error is returned with the message "The refresh token is expired."
   - If the refresh token is successfully parsed and validated, a new access token is generated using the tenant's information from the refresh token claims.
*/

func (n *sTenant) RefreshToken(ctx context.Context, refreshToken string) (accessToken string, err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Refresh token [%s]", refreshToken)
	claims, err := n.parseToken(ctx, refreshToken)
	if err != nil {
		g.Log().Cat(consts.ERROR).Error(ctx, err)
		return
	}
	isValid := false
	isValid, _ = n.checkToken(ctx, claims)
	if !isValid {
		err = gerror.NewCode(consts.ErrorTokenExpired)
		return
	}
	// 重新生成access token
	g.Log().Cat(consts.DEBUG).Debugf(ctx, "Refresh token, tenantID :%s", claims.TenantID)
	// 校验Tenant 信息是否正确
	ti, err := n.getTenantInfoByIDFromCache(ctx, claims.TenantID)
	if err == nil && ti.Password == claims.Password && ti.APPKey == claims.AppKey && ti.Available {
		accessToken, err = n.createTokens(ctx, claims.TenantID, claims.Password, claims.AppKey, consts.GenAccessToken)
	} else {
		// 没有在缓存中找到在从实体中查找
		isExist := false
		isExist, _, err = n.tenantIsExisted(ctx, claims.TenantID, true)
		if err != nil {
			g.Log().Cat(consts.ERROR).Error(ctx, err)
			return
		} else {
			if !isExist {
				err = gerror.NewCode(consts.ErrorTenantNotExist)
				return
			}

			props := g.Map{}
			err, props = n.validateTenantPass(ctx, claims.TenantID, claims.Password, true)
			if err != nil {
				g.Log().Cat(consts.ERROR).Error(ctx, err)
				return
			} else {
				if props[consts.ProAppKey].(string) == claims.AppKey {
					accessToken, err = n.createTokens(ctx, claims.TenantID, claims.Password, claims.AppKey, consts.GenAccessToken)
				}
			}
		}
	}

	return
}

// AuthTenant authenticates a tenant using Weaviate data.
// It checks if the tenant exists and gets the object ID.
// The tenant object is retrieved by ID.
// The app key is extracted from the properties.
// This key is compared with the passed app key.
// If mismatched, an error is returned indicating unauthorized.
// On match, authentication succeeds.
// This provides the ability to validate tenants against
// the data stored in Weaviate.
func (n *sTenant) AuthTenant(ctx context.Context, token string) (err error) {
	g.Log().Cat(consts.INFO).Infof(ctx, "Authorized by token [%s]", gstr.HideStr(token, 50, "*"))

	claims, err := n.parseToken(ctx, token)
	if err != nil {
		g.Log().Cat(consts.ERROR).Debug(ctx, err)
		return
	}
	isValid := false
	isValid, _ = n.checkToken(ctx, claims)
	if !isValid {
		err = gerror.NewCode(consts.ErrorTokenExpired)
		g.Log().Cat(consts.ERROR).Debug(ctx, err)
		return
	}

	// 先从缓存中查找
	ti, err := n.getTenantInfoByIDFromCache(ctx, claims.TenantID)
	if err == nil && ti.Password == claims.Password && ti.APPKey == claims.AppKey {
		return
	}

	isExist, _, err := n.tenantIsExisted(ctx, claims.TenantID)
	if err != nil {
		g.Log().Cat(consts.ERROR).Debug(ctx, err)
		return err
	}
	if !isExist {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Tenant %s is not exist", claims.TenantID)
		err = gerror.Newf("Tenant %s is not exist", claims.TenantID)
		return
	}

	err, props := n.validateTenantPass(ctx, claims.TenantID, claims.Password)
	if err == nil && props[consts.ProAppKey].(string) == claims.AppKey {
		return
	}

	return
}
