package messageQ

import (
	"context"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/text/gstr"
	amqp "github.com/rabbitmq/amqp091-go"
	"tenants/boot"
	"tenants/internal/consts"
	"tenants/internal/service"
)

var ReadyMessageQ = make(chan struct{})

func init() {
	service.RegisterMessageQ(New())
	close(ReadyMessageQ)

}

type sMessageQ struct {
	conn    *amqp.Connection
	channel *amqp.Channel
}

func FailOnError(err error) {
	if err != nil {
		panic(err)
	}
}

func New() service.IMessageQ {
	boot.WaitReady()
	g.Log().Notice(context.TODO(), "ready to create MQ")

	s := &sMessageQ{}
	ctx := gctx.GetInitCtx()

	vUrl, err := g.Cfg().Get(ctx, "rabbitMQ.url", "")
	FailOnError(err)
	if vUrl == nil || vUrl.IsEmpty() {
		panic("rabbitMQ.url is nil")
	}

	s.conn, err = amqp.Dial(vUrl.String())
	FailOnError(err)

	s.channel, err = s.conn.Channel()
	FailOnError(err)

	err = s.channel.ExchangeDeclare(
		consts.ExchangeName,
		"direct",
		true,
		false,
		false,
		false,
		nil,
	)
	FailOnError(err)

	return s
}

func (s *sMessageQ) logger() glog.ILogger {
	return g.Log().Cat(consts.CatMQ)
}

func (s *sMessageQ) Send(ctx context.Context, routeKey, action string, data []byte, additionalRouteKeys ...string) (err error) {
	s.logger().Debugf(ctx, "Send: route key %q, action %q  ", routeKey, action)
	err = s.channel.PublishWithContext(
		ctx,
		consts.ExchangeName,
		routeKey,
		true,
		false,
		amqp.Publishing{
			ContentType: "application/json",
			Body:        data,
			Type:        action,
		},
	)
	if err != nil {
		s.logger().Error(ctx, err)
		return
	}
	if len(additionalRouteKeys) == 2 {
		additionalRouteKey := additionalRouteKeys[0]
		params := additionalRouteKeys[1]
		eleValues := gstr.Split(params, ":")
		if len(eleValues) != 2 {
			return
		}
		// tenant_id : xxx
		jsData := gjson.New(nil)
		_ = jsData.Set(eleValues[0], eleValues[1])

		err = s.channel.PublishWithContext(
			ctx,
			consts.ExchangeName,
			additionalRouteKey,
			true,
			false,
			amqp.Publishing{
				ContentType: "application/json",
				Body:        []byte(jsData.MustToJsonString()),
				Type:        action,
			},
		)
		if err != nil {
			s.logger().Error(ctx, err)
			return
		}
	}

	return
}
