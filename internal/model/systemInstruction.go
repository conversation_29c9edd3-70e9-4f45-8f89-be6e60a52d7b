package model

import (
	"github.com/gogf/gf/v2/frame/g"
)

type SystemInstruction struct {
	System              string                  `json:"system"`
	ServiceInstructions []ServiceSysInstruction `json:"service_instructions"`
}

func (r *SystemInstruction) IsEmpty() bool {
	if len(r.ServiceInstructions) == 0 && g.IsEmpty(r.System) {
		return true
	}
	if g.IsEmpty(r.System) {
		return true
	}
	return false
}

type ServiceSysInstruction struct {
	ServiceID      string `json:"service_id"`
	Channel        string `json:"channel"`
	SysInstruction string `json:"sys_instruction"`
}
