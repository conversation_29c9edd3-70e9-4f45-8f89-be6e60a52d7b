package model

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/weaviate/weaviate-go-client/v5/weaviate/filters"
	"github.com/weaviate/weaviate/entities/models"
)

type VectorCollectionSetting struct {
	CollectionName   string             `json:"collection_name"`
	MultiTenancy     bool               `json:"multi_tenancy"`
	VectorProperties []string           `json:"vector_properties"`
	Properties       []*models.Property `json:"properties"`
}
type CreateCollectionInput struct {
	VectorCollectionSetting
	Tenants       []string `json:"tenants"`
	RenewSettings bool
}

type AddNewPropertiesInput struct {
	CollectionName string             `json:"collection_name" v:"required"`
	Properties     []*models.Property `json:"properties"`
}
type EmptyCollectionInput struct {
	CollectionName string `json:"collection_name" v:"required"`
	Tenant         string `json:"tenant"`
}
type ClearDataByFilterInput struct {
	CollectionName string `json:"collection_name" v:"required"`
	Tenant         string `json:"tenant"`
	Filter         *filters.WhereBuilder
}
type DeleteTenantsInput struct {
	Collections []string `json:"collections" v:"required"`
	Tenants     []string `json:"tenants" v:"required"`
}
type CollectionData struct {
	ID         string    `json:"id"`
	Tenant     string    `json:"tenant"`
	Collection string    `json:"collection"`
	Vector     []float32 `json:"-"`
	Properties g.Map     `json:"properties"`
}

type CreateDataInput struct {
	Data []*CollectionData `json:"data"`
}
type CreateDataOutput struct {
	IDs     []string `json:"ids"`
	Total   int      `json:"total"`
	Success int      `json:"success"`
	Fail    int      `json:"fail"`
}
type FetchRecordsInput struct {
	Tenant             string                `json:"tenant" `
	Collection         string                `json:"collection" v:"required"`
	Properties         []string              `json:"properties" v:"required"`
	PageSize           int                   `json:"page_size" v:"required"`
	Offset             int                   `json:"offset"`
	Filter             *filters.WhereBuilder `json:"filter"`
	OriginalAdditional bool                  `json:"original_additional"`
}
type FetchRecordsOutput struct {
	Records []g.Map `json:"records"`
}

func (f FetchRecordsInput) String() string {
	js := gjson.New(f)
	if f.Filter != nil {
		_ = js.Set("filter", f.Filter.String())
	}
	return js.MustToJsonIndentString()
}

type GetAllRecordsInput struct {
	Tenant     string                `json:"tenant" `
	Collection string                `json:"collection" v:"required"`
	Properties []string              `json:"properties" v:"required"`
	PageSize   int                   `json:"page_size" v:"required"`
	Filter     *filters.WhereBuilder `json:"filter"`
}

func (f GetAllRecordsInput) String() string {
	js := gjson.New(f)
	if f.Filter != nil {
		_ = js.Set("filter", f.Filter.String())
	}
	return js.MustToJsonIndentString()
}

type GetAllRecordsOutput struct {
	Records []map[string]any `json:"records"`
}
type GetPropertiesInput struct {
	Tenant     string   `json:"tenant" `
	Collection string   `json:"collection" v:"required"`
	IDs        []string `json:"id" v:"required"`
	WithVector bool     `json:"with_vector"`
}

type GetPropertiesOutput struct {
	IDToProperties map[string]map[string]any `json:"id_to_properties"`
}
type GetPropertiesByGroupInput struct {
	Tenant            string                `json:"tenant"`
	Collection        string                `json:"collection" v:"required"`
	Properties        []string              `json:"properties" v:"required"`
	Filter            *filters.WhereBuilder `json:"filter" v:"required"`
	GroupedFields     []string              `json:"grouped_fields" v:"required"`
	MaxGroups         int                   `json:"max_groups" v:"required"`
	MaxObjectPerGroup int                   `json:"max_object_per_group" v:"required"`
	KeyProp           string                `json:"key_prop" `
	ValueProp         string                `json:"value_prop"`
}
type GetPropertiesByGroupOutput struct {
	OutputMap  map[string]string `json:"output_map"`
	OutputData []map[string]any  `json:"output_data"`
}
type UpdatePropertiesInput struct {
	Tenant     string    `json:"tenant"`
	Collection string    `json:"collection" v:"required"`
	ID         string    `json:"id" v:"required"`
	Properties g.Map     `json:"properties" `
	Vector     []float32 `json:"-"`
}

type CreateTenantIfNotExistInput struct {
	Tenant      string   `json:"tenant" v:"required"`
	Collections []string `json:"collections" v:"required"`
}
type SimilaritySearchInput struct {
	Tenant             string                `json:"tenant"`
	Collection         string                `json:"collection" v:"required"`
	Properties         []string              `json:"properties" v:"required"`
	Vector             []float32             `json:"-" v:"required"`
	Distance           float32               `json:"distance" v:"required"`
	Limit              int                   `json:"limit" v:"required"`
	Filter             *filters.WhereBuilder `json:"filter"`
	OriginalAdditional bool                  `json:"original_additional"`
}

func (f SimilaritySearchInput) String() string {
	js := gjson.New(f)
	if f.Filter != nil {
		_ = js.Set("filter", f.Filter.String())
	}
	return js.MustToJsonIndentString()
}

type SimilaritySearchOutput struct {
	Properties []map[string]any `json:"properties"`
}
type HybridSearchInput struct {
	Tenant             string                `json:"tenant" `
	Collection         string                `json:"collection" v:"required"`
	Query              string                `json:"query" v:"required"`
	Properties         []string              `json:"properties" v:"required"`
	Vector             []float32             `json:"-" v:"required"`
	Filter             *filters.WhereBuilder `json:"filter" v:"required"`
	Alpha              float32               `json:"alpha" v:"required"`
	PropertyWeight     []string              `json:"property_weight" v:"required"`
	FusionType         string                `json:"fusion_type" v:"required"`
	Limit              int                   `json:"limit" v:"required"`
	OriginalAdditional bool                  `json:"original_additional"`
}

func (i *HybridSearchInput) String() string {
	js := gjson.New(i)
	if i.Filter != nil {
		_ = js.Set("filter", i.Filter.String())
	}
	return js.MustToJsonIndentString()
}

type HybridSearchOutput struct {
	Properties []map[string]any `json:"properties"`
}
