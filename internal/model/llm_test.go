package model

import (
	"testing"
)

func TestLLMConfiguration_Equals(t *testing.T) {
	config1 := &LLMConfiguration{
		LLMName:        "test-llm",
		Description:    "Test LLM",
		BaseUrl:        "https://api.example.com",
		Token:          "secret-token",
		ResourceName:   "test-resource",
		EmbeddingModel: "embedding-model",
		ModelId:        "model-123",
		APIVersion:     "v1",
		Temperature:    0.7,
		MaxToken:       1000,
		LLMType:        "openai",
	}

	config2 := &LLMConfiguration{
		LLMName:        "test-llm",
		Description:    "Test LLM",
		BaseUrl:        "https://api.example.com",
		Token:          "secret-token",
		ResourceName:   "test-resource",
		EmbeddingModel: "embedding-model",
		ModelId:        "model-123",
		APIVersion:     "v1",
		Temperature:    0.7,
		MaxToken:       1000,
		LLMType:        "openai",
	}

	config3 := &LLMConfiguration{
		LLMName:        "different-llm",
		Description:    "Test LLM",
		BaseUrl:        "https://api.example.com",
		Token:          "secret-token",
		ResourceName:   "test-resource",
		EmbeddingModel: "embedding-model",
		ModelId:        "model-123",
		APIVersion:     "v1",
		Temperature:    0.7,
		MaxToken:       1000,
		LLMType:        "openai",
	}

	// Test equal configurations
	if !config1.Equals(config2) {
		t.Error("Expected config1 and config2 to be equal")
	}

	// Test different configurations
	if config1.Equals(config3) {
		t.Error("Expected config1 and config3 to be different")
	}

	// Test nil handling
	if config1.Equals(nil) {
		t.Error("Expected config1 and nil to be different")
	}

	var nilConfig *LLMConfiguration
	if !nilConfig.Equals(nil) {
		t.Error("Expected nil config and nil to be equal")
	}
}

func TestLLMConfiguration_IsSame(t *testing.T) {
	config1 := &LLMConfiguration{
		LLMName:     "test-llm",
		Description: "Test LLM",
		Temperature: 0.7,
		MaxToken:    1000,
	}

	config2 := &LLMConfiguration{
		LLMName:     "test-llm",
		Description: "Test LLM",
		Temperature: 0.7,
		MaxToken:    1000,
	}

	config3 := &LLMConfiguration{
		LLMName:     "different-llm",
		Description: "Test LLM",
		Temperature: 0.7,
		MaxToken:    1000,
	}

	// Test with direct pointer comparison
	if !config1.IsSame(config2) {
		t.Error("Expected config1 and config2 to be the same")
	}

	// Test with different configurations
	if config1.IsSame(config3) {
		t.Error("Expected config1 and config3 to be different")
	}

	// Test with interface{}
	var anyConfig interface{} = config2
	if !config1.IsSame(anyConfig) {
		t.Error("Expected config1 and anyConfig to be the same")
	}

	// Test with nil
	if config1.IsSame(nil) {
		t.Error("Expected config1 and nil to be different")
	}
}

func TestLLMConfiguration_EqualsExcludingSensitive(t *testing.T) {
	config1 := &LLMConfiguration{
		LLMName:     "test-llm",
		Description: "Test LLM",
		Token:       "secret-token-1",
		Temperature: 0.7,
		MaxToken:    1000,
	}

	config2 := &LLMConfiguration{
		LLMName:     "test-llm",
		Description: "Test LLM",
		Token:       "secret-token-2", // Different token
		Temperature: 0.7,
		MaxToken:    1000,
	}

	// Should be equal when excluding sensitive fields
	if !config1.EqualsExcludingSensitive(config2) {
		t.Error("Expected configs to be equal when excluding sensitive fields")
	}

	// Should be different when including sensitive fields
	if config1.Equals(config2) {
		t.Error("Expected configs to be different when including token")
	}
}

func TestLLMConfiguration_Hash(t *testing.T) {
	config1 := &LLMConfiguration{
		LLMName:     "test-llm",
		Description: "Test LLM",
		Temperature: 0.7,
		MaxToken:    1000,
	}

	config2 := &LLMConfiguration{
		LLMName:     "test-llm",
		Description: "Test LLM",
		Temperature: 0.7,
		MaxToken:    1000,
	}

	config3 := &LLMConfiguration{
		LLMName:     "different-llm",
		Description: "Test LLM",
		Temperature: 0.7,
		MaxToken:    1000,
	}

	hash1 := config1.Hash()
	hash2 := config2.Hash()
	hash3 := config3.Hash()

	// Same configurations should have same hash
	if hash1 != hash2 {
		t.Error("Expected same configurations to have same hash")
	}

	// Different configurations should have different hash
	if hash1 == hash3 {
		t.Error("Expected different configurations to have different hash")
	}

	// Hash should not be empty
	if hash1 == "" {
		t.Error("Expected hash to not be empty")
	}

	// Nil configuration should return empty hash
	var nilConfig *LLMConfiguration
	if nilConfig.Hash() != "" {
		t.Error("Expected nil configuration to return empty hash")
	}
}

// Benchmark tests to compare performance
func BenchmarkLLMConfiguration_Equals(b *testing.B) {
	config1 := &LLMConfiguration{
		LLMName:        "test-llm",
		Description:    "Test LLM",
		BaseUrl:        "https://api.example.com",
		Token:          "secret-token",
		ResourceName:   "test-resource",
		EmbeddingModel: "embedding-model",
		ModelId:        "model-123",
		APIVersion:     "v1",
		Temperature:    0.7,
		MaxToken:       1000,
		LLMType:        "openai",
	}

	config2 := &LLMConfiguration{
		LLMName:        "test-llm",
		Description:    "Test LLM",
		BaseUrl:        "https://api.example.com",
		Token:          "secret-token",
		ResourceName:   "test-resource",
		EmbeddingModel: "embedding-model",
		ModelId:        "model-123",
		APIVersion:     "v1",
		Temperature:    0.7,
		MaxToken:       1000,
		LLMType:        "openai",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		config1.Equals(config2)
	}
}

func BenchmarkLLMConfiguration_Hash(b *testing.B) {
	config := &LLMConfiguration{
		LLMName:        "test-llm",
		Description:    "Test LLM",
		BaseUrl:        "https://api.example.com",
		Token:          "secret-token",
		ResourceName:   "test-resource",
		EmbeddingModel: "embedding-model",
		ModelId:        "model-123",
		APIVersion:     "v1",
		Temperature:    0.7,
		MaxToken:       1000,
		LLMType:        "openai",
	}

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		config.Hash()
	}
}