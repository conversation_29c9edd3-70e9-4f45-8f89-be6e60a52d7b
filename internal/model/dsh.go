package model

import "github.com/gogf/gf/v2/frame/g"

/* for dataSyncHub request and response*/

type GetContentsReq struct {
	Schema    string  `json:"schema"`
	Table     string  `json:"table"`
	WhereCond string  `json:"where_cond"`
	Params    g.<PERSON>lice `json:"params"`
	<PERSON>    g.<PERSON>lice `json:"fields"`
	Limit     int     `json:"limit"`
	RawSQL    string  `json:"raw_sql"`
	Order     string  `json:"order"`
}

type GetContentsRes struct {
	Contents []map[string]any `json:"contents"`
	Code     int              `json:"code"`
	Message  string           `json:"message"`
	Cost     string           `json:"cost"`
}

type BatchSqlReq struct {
	Schema  string   `json:"schema"`
	SQLList []string `json:"sql_list"`
}
type BatchSqlRes struct {
	Code         int      `json:"code"`
	Message      string   `json:"message"`
	Cost         string   `json:"cost"`
	TotalCount   int      `json:"total_count"`
	SuccessCount int      `json:"success_count"`
	FailCount    int      `json:"fail_count"`
	Errors       []string `json:"errors"`
}
