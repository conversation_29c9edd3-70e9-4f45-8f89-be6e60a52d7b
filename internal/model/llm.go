package model

import (
	"crypto/sha256"
	"encoding/hex"

	"github.com/gogf/gf/v2/util/gconv"
)

type LLMConfiguration struct {
	LLMName        string  `json:"llm_name"`
	Description    string  `json:"description"`
	BaseUrl        string  `json:"base_url"`
	Token          string  `json:"token"`
	ResourceName   string  `json:"resource_name"`
	EmbeddingModel string  `json:"embedding_model"`
	ModelId        string  `json:"model"`
	APIVersion     string  `json:"api_version"`
	Temperature    float32 `json:"temperature"`
	MaxToken       int     `json:"max_token"`
	LLMType        string  `json:"llm_type"`
}

// Equals performs a field-by-field comparison for better performance and clarity
func (l *LLMConfiguration) Equals(other *LLMConfiguration) bool {
	if l == nil || other == nil {
		return l == other
	}
	
	return l.LLMName == other.LLMName &&
		l.Description == other.Description &&
		l.BaseUrl == other.BaseUrl &&
		l.Token == other.Token &&
		l.ResourceName == other.ResourceName &&
		l.EmbeddingModel == other.EmbeddingModel &&
		l.ModelId == other.ModelId &&
		l.APIVersion == other.APIVersion &&
		l.Temperature == other.Temperature &&
		l.MaxToken == other.MaxToken &&
		l.LLMType == other.LLMType
}

// Hash returns a SHA256 hash of the configuration for caching purposes
func (l *LLMConfiguration) Hash() string {
	if l == nil {
		return ""
	}
	
	hash := sha256.New()
	hash.Write(gconv.Bytes(l))
	return hex.EncodeToString(hash.Sum(nil))
}

// IsSame maintains compatibility with existing code but fixes the logic
// The original implementation had a critical bug where the same hash instance was reused,
// causing incorrect comparisons. This version fixes the logic and adds proper error handling.
func (l *LLMConfiguration) IsSame(o any) bool {
	if l == nil {
		return o == nil
	}
	
	// Try direct type assertion first for better performance
	if other, ok := o.(*LLMConfiguration); ok {
		return l.Equals(other)
	}
	
	// Try conversion for backward compatibility
	var d *LLMConfiguration
	if err := gconv.Scan(o, &d); err != nil || d == nil {
		return false
	}
	
	return l.Equals(d)
}

// EqualsExcludingSensitive compares configurations excluding sensitive fields like Token
// This is useful for comparing configurations without exposing sensitive information
func (l *LLMConfiguration) EqualsExcludingSensitive(other *LLMConfiguration) bool {
	if l == nil || other == nil {
		return l == other
	}
	
	return l.LLMName == other.LLMName &&
		l.Description == other.Description &&
		l.BaseUrl == other.BaseUrl &&
		// l.Token == other.Token,  // Excluded for security
		l.ResourceName == other.ResourceName &&
		l.EmbeddingModel == other.EmbeddingModel &&
		l.ModelId == other.ModelId &&
		l.APIVersion == other.APIVersion &&
		l.Temperature == other.Temperature &&
		l.MaxToken == other.MaxToken &&
		l.LLMType == other.LLMType
}
