package model

import "github.com/gogf/gf/v2/container/garray"

// ServiceDef  在配置文件中定义服务, 内部服务的key 和可以访问的微服务和接口
type ServiceDef struct {
	ServiceName string `json:"service_name"`
	Key         string `json:"key"`
	Include     []struct {
		MicroService string   `json:"micro_service"`
		Actions      []string `json:"actions"`
	}
}

func (sd *ServiceDef) IsOK(key, microService, action string) bool {
	if key != sd.Key {
		return false
	}
	for _, s := range sd.Include {
		if s.MicroService == microService {
			return garray.NewStrArrayFrom(s.Actions).Contains(action)
		}
	}
	return false
}
