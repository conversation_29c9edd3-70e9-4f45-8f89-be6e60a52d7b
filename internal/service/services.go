package service

import (
	"context"
	"sync"
	"tenants/internal/consts"
	"tenants/internal/model"

	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/gtimer"

	"github.com/gogf/gf/container/gmap"
)

func init() {
	ctx := gctx.GetInitCtx()
	readServiceDefination(ctx)

	vDur, _ := g.Cfg().Get(ctx, "system.update_services_duration", "1m")

	gtimer.SetInterval(ctx, vDur.Duration(), readServiceDefination)
}

var (
	mServices = gmap.NewStrAnyMap(true)
	mu        = sync.RWMutex{}
)

func readServiceDefination(ctx context.Context) {
	vServices, err := g.Cfg().Get(ctx, "services")
	if err == nil {
		var services []model.ServiceDef
		err = vServices.Structs(&services)
		if err == nil {
			mServices.Clear()
			for _, service := range services {
				mServices.Set(service.ServiceName, service)
			}

		}
	}
}
func ValidateService(ctx context.Context, serviceName, key, miroService, action string) (isExist bool, result bool) {
	mu.Lock()
	defer mu.Unlock()
	g.Log().Cat(consts.INFO).Infof(ctx, "Verify the service %s, micro-service: %s , action:%s ", serviceName, miroService, action)
	if !mServices.Contains(serviceName) {
		return
	}
	isExist = true
	var service *model.ServiceDef
	s := mServices.GetVar(serviceName)
	if s.IsNil() {
		g.Log().Cat(consts.DEBUG).Debugf(ctx, "Can not found %s settings", serviceName)

		return
	}
	_ = s.Scan(&service)
	if service != nil {
		result = service.IsOK(key, miroService, action)
		return
	} else {
		g.Log().Cat(consts.DEBUG).Debug(ctx, "Convert object failed ")

		return
	}
}
