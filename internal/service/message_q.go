// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
)

type (
	IMessageQ interface {
		Send(ctx context.Context, routeKey string, action string, data []byte, additionalRouteKeys ...string) (err error)
	}
)

var (
	localMessageQ IMessageQ
)

func MessageQ() IMessageQ {
	if localMessageQ == nil {
		panic("implement not found for interface IMessageQ, forgot register?")
	}
	return localMessageQ
}

func RegisterMessageQ(i IMessageQ) {
	localMessageQ = i
}
