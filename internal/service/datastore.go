// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"
	"tenants/internal/model"

	"github.com/gogf/gf/v2/encoding/gjson"
)

type (
	IDataStore interface {
		ExecuteBatchSql(ctx context.Context, in model.BatchSqlReq) (res *model.BatchSqlRes, err error)
		// WriteLLMParams is a method of the sDataStore struct.
		// It writes the provided LLM parameters into the database.
		// It first logs the LLM parameters and then scans them into an LLMConfiguration struct.
		// It then starts a transaction and attempts to replace the existing LLM parameters in the database with the new ones.
		// If an error occurs during any of these operations, it logs the error and returns it wrapped with the error code specified in the constant ErrorGeneral.
		//
		// Parameters:
		// ctx : context.Context - The context in which the method is called.
		// llmParams : any - The new LLM parameters to be written into the database.
		//
		// Returns:
		// err : error - The error that occurred during the execution of the method, if any.
		WriteLLMParams(ctx context.Context, llmParams any) (err error)
		// RemoveLLMParams is a method of the sDataStore struct.
		// It removes the LLM (Language Learning Model) parameters from the database.
		//
		// The method first starts a transaction and attempts to delete the LLM parameters from the TableLLMParams table in the database.
		// If an error occurs during this operation, it logs the error and returns it wrapped with the error code specified in the constant ErrorGeneral.
		// If the operation is successful, it starts another transaction and attempts to delete the LLM from the TableTenantLLM table in the database.
		// If an error occurs during this operation, it logs the error and returns it wrapped with the error code specified in the constant ErrorGeneral.
		//
		// Parameters:
		// ctx : context.Context - The context in which the method is called.
		// llmName : string - The name of the LLM to be removed from the database.
		//
		// Returns:
		// err : error - The error that occurred during the execution of the method, if any.
		RemoveLLMParams(ctx context.Context, llmName string) (err error)
		GetSysInstructionByTenantID(ctx context.Context, tenantID string) (prompts *model.SystemInstruction, err error)
		WriteSystemInstruction(ctx context.Context, tenantID string, prompts *model.SystemInstruction) (err error)
		// AssignLLMToTenant is a method of the sDataStore struct.
		// It assigns a Language Learning Model (LLM) to a tenant in the database and updates the cache.
		//
		// The method first logs the tenant ID and the LLM name.
		// It then starts a transaction and attempts to replace the existing LLM of the tenant in the database with the new one.
		// If an error occurs during this operation, it logs the error and returns it wrapped with the error code specified in the constant ErrorGeneral.
		// If the operation is successful, it updates the cache with the new LLM of the tenant using the setLLMTenantToCache method of the sDataStore struct.
		//
		// Parameters:
		// ctx : context.Context - The context in which the method is called.
		// tenantId : string - The ID of the tenant to which the LLM should be assigned.
		// llmName : string - The name of the LLM to be assigned to the tenant.
		//
		// Returns:
		// err : error - The error that occurred during the execution of the method, if any.
		AssignLLMToTenant(ctx context.Context, tenantId string, llmName string) (err error)
		// RemoveTenantLLM is a method of the sDataStore struct.
		// It removes the specified Language Learning Models (LLMs) from a tenant in the database and updates the cache.
		//
		// The method first logs the tenant ID and the LLM names.
		// It then starts a transaction and attempts to delete the specified LLMs of the tenant from the database.
		// If an error occurs during this operation, it logs the error and returns it.
		// If the operation is successful, it updates the cache by removing the specified LLMs of the tenant.
		//
		// Parameters:
		// ctx : context.Context - The context in which the method is called.
		// tenantId : string - The ID of the tenant from which the LLMs should be removed.
		// llms : []string - The names of the LLMs to be removed from the tenant.
		//
		// Returns:
		// err : error - The error that occurred during the execution of the method, if any.
		RemoveTenantLLM(ctx context.Context, tenantId string, llms []string) (err error)
		GetLLMs(ctx context.Context, detail bool) (ret *gjson.Json)
		GetTenantLLM(ctx context.Context, tenantId string) (ret *gjson.Json)
		GetLLMTenantsList(ctx context.Context) []*gjson.Json
		SetTenantParams(ctx context.Context, tenantId string, params string) (err error)
		GetTenantParams(ctx context.Context, tenantId string) (params string, err error)
		// CreatePartitionTablesForTenant 為特定租戶創建分區表
		// 入參為 tenant_id，為該租戶創建所有配置的分區表
		CreatePartitionTablesForTenant(ctx context.Context, tenantID string) error
	}
)

var (
	localDataStore IDataStore
)

func DataStore() IDataStore {
	if localDataStore == nil {
		panic("implement not found for interface IDataStore, forgot register?")
	}
	return localDataStore
}

func RegisterDataStore(i IDataStore) {
	localDataStore = i
}
