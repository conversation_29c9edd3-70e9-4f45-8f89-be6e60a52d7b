// ================================================================================
// Code generated and maintained by GoFrame CLI tool. DO NOT EDIT.
// You can delete these comments if you wish manually maintain this interface file.
// ================================================================================

package service

import (
	"context"

	"github.com/gogf/gf/v2/encoding/gjson"
)

type (
	ITenant interface {
		GetTenantAvailable(ctx context.Context, tenantID string) (available bool, err error)
		NewTokens(ctx context.Context, tenantID string, password string, appKey string) (accessToken string, refreshToken string, err error)
		// createTenant creates a new tenant in Weaviate.
		// It builds the data schema with properties:
		// - Tenant_Id: The tenant ID
		// - OldPass: The password
		// - Available: Set to true
		// The client is used to send a data creation request
		// to the Tenants class with this schema.
		// Any error from the request is returned.
		// On success, the tenant is added to Weaviate storage.
		// This allows persisting new tenants after validation.
		CreateTenant(ctx context.Context, tenantID string, password string) (err error)
		DisableTenant(ctx context.Context, tenantID string, marked bool) (err error)
		// ActivateTenant activates a tenant in Weaviate by setting Available to true.
		// It first checks if the tenant exists and gets the object ID.
		// A data update request is made using the client to update the
		// tenant record with Available set to true.
		// Any error from the request is returned.
		// On success, the tenant is activated in Weaviate by having
		// Available set to true.
		// This provides the logic to activate a tenant account.
		//
		// Parameters:
		// ctx: The context to use for the request.
		// tenantID: The ID of the tenant to activate.
		//
		// Returns:
		// err: Any error that occurred during the operation, otherwise nil.
		ActivateTenant(ctx context.Context, tenantID string) (err error)
		Remark(ctx context.Context, tenantId string, remark string) error
		// UpdateTenant updates the password for a tenant in Weaviate.
		// It builds a property map with the new password.
		// It calls updateTenant to update the tenant with this map.
		// Any error is logged and returned.
		// On success, the tenant's password is updated in Weaviate.
		// This provides the logic to update a tenant's password.
		UpdateTenant(ctx context.Context, tenantID string, oldPassword string, newPassword string, isAdmin bool) (err error)
		// ListTenants retrieves all tenants from Weaviate.
		// It uses pagination to fetch tenants from the objects API.
		// A callback function is defined to parse each tenant object
		// into a TenantInfo struct.
		// Objects are fetched in batches using limit and after params.
		// The next batch uses the last ID as the after param.
		// This continues until no more objects are returned.
		// All TenantInfo structs are added to an array.
		// The array is converted to JSON and returned.
		// Any errors from requests are returned.
		// This provides the full logic to paginate and retrieve all
		// tenants from Weaviate.
		ListTenants(ctx context.Context) (tenants *gjson.Json, err error)
		GenNewAppKeyForTenant(ctx context.Context, tenantID string, password string, isAdmin bool) (err error, newKey string)
		RefreshToken(ctx context.Context, refreshToken string) (accessToken string, err error)
		// AuthTenant authenticates a tenant using Weaviate data.
		// It checks if the tenant exists and gets the object ID.
		// The tenant object is retrieved by ID.
		// The app key is extracted from the properties.
		// This key is compared with the passed app key.
		// If mismatched, an error is returned indicating unauthorized.
		// On match, authentication succeeds.
		// This provides the ability to validate tenants against
		// the data stored in Weaviate.
		AuthTenant(ctx context.Context, token string) (err error)
	}
)

var (
	localTenant ITenant
)

func Tenant() ITenant {
	if localTenant == nil {
		panic("implement not found for interface ITenant, forgot register?")
	}
	return localTenant
}

func RegisterTenant(i ITenant) {
	localTenant = i
}
