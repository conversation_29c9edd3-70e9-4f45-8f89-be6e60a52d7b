package cmd

import (
	"context"

	"tenants/utility"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/net/ghttp"
	"github.com/gogf/gf/v2/os/gcmd"
	"tenants/internal/consts"
	"tenants/internal/controller/tenants"
)

var Main = gcmd.Command{
	Name:  "main",
	Usage: "main",
	Brief: "start http server",
	Func: func(ctx context.Context, parser *gcmd.Parser) (err error) {
		s := g.Server(consts.ServiceName)
		s.Group("/", func(group *ghttp.RouterGroup) {
			group.Middleware(ghttp.MiddlewareHandlerResponse, MiddleHandler)
			group.Bind(
				tenants.NewV1(),
			)
		})
		s.Run()
		return nil
	},
}

func MiddleHandler(r *ghttp.Request) {
	// 拦截处理所有的request 请求在中间件中处理
	ctx := r.GetCtx()
	r.Response.CORSDefault()
	if gjson.Valid(r.GetBodyString()) {
		g.Log().Cat(consts.RR).Debugf(
			ctx,
			"Request-Uri: %s Body:%s",
			r.RequestURI,
			utility.HideStr(gjson.New(r.GetBodyString()), consts.SensitiveWords, 100),
		)
	} else {
		g.Log().Cat(consts.RR).Debugf(ctx, "Request-Uri: %s", r.RequestURI)
	}
	r.Middleware.Next()
	if gjson.Valid(r.Response.BufferString()) {
		g.Log().Cat(consts.RR).Debugf(ctx, "Response-%s", r.Response.BufferString())
	} else {
		g.Log().Cat(consts.RR).Debug(ctx, "Return  response... ")
	}
}
