package consts

import (
	"github.com/gogf/gf/v2/errors/gcode"
)

var (
	Success      = gcode.New(0, "success", nil)
	ErrorGeneral = gcode.New(-1, "System error", nil)

	ErrorSameTenantID   = gcode.New(1000, "ID for the same tenant already exists.", nil)
	ErrorFormatTenantID = gcode.New(1001, `tenant id does not comply with the specification Tenant names can only contain alphanumeric characters (a-z, A-Z, 0-9), underscores (_), and hyphens (-), and can be 4 to 64 characters long.`, nil)
	ErrorFormatPassword = gcode.New(1002, `password does not conform to the specification.Be at least 8 characters long. Contains alphanumeric and special characters.`, nil)
	ErrorTenantNotExist = gcode.New(1003, "tenant does not exist.", nil)
	ErrorPassword       = gcode.New(1004, "Enter  wrong password", nil)
	ErrorAppKey         = gcode.New(1005, "Enter  wrong app key", nil)
	ErrorTokenExpired   = gcode.New(1006, "token has expired", nil)
)
