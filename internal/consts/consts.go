package consts

import (
	"github.com/gogf/gf/v2/frame/g"
	"github.com/weaviate/weaviate/entities/models"
)

const (
	ERROR   = "error"
	INFO    = "info"
	DEBUG   = "debug"
	RR      = "http-logs"
	WARNING = "warning"
)
const (
	CatMQ = "MQ"
)

const Tenants = "Tenants"

var ServiceName = "tenants.svc"

const HeaderAzureKey = "X-Azure-Api-Key"
const (
	ProTenantID    = "tenant_Id"
	ProPassword    = "password"
	ProAppKey      = "app_Key"
	ProAvailable   = "available"
	ProRemark      = "remark"
	HeaderTenantID = "tenant_id"
	HeaderAppKey   = "app_key"
)

var moduleConfig = map[string]interface{}{
	"text2vec-openai": map[string]interface{}{
		"skip":                  true,
		"vectorizePropertyName": false,
	},
}

var Properties = []*models.Property{
	{
		DataType:     []string{"text"},
		Name:         ProTenantID,
		ModuleConfig: moduleConfig,
	},
	{
		DataType:     []string{"text"},
		Name:         ProPassword,
		ModuleConfig: moduleConfig,
	},
	{
		DataType:     []string{"uuid"},
		Name:         ProAppKey,
		ModuleConfig: moduleConfig,
	},
	{
		DataType:     []string{"boolean"},
		Name:         ProAvailable,
		ModuleConfig: moduleConfig,
	},
	{
		DataType:     []string{"text"},
		Name:         ProRemark,
		ModuleConfig: moduleConfig,
	},
}

const (
	GenAccessToken = iota
	GenRefreshToken
)

var TableNameScript = g.MapStrStr{
	TableLLMParams:    ScriptLLMParams,
	TableTenantLLM:    ScriptTenantLLM,
	TableTenantParams: ScriptTenantParams,
	TableTenant:       ScriptTenant,
	TableTenantPrompt: ScriptTenantPrompt,
}

const (
	DataBaseDSH = "dsh"

	TableLLMParams    = "llm_params"
	TableTenantLLM    = "tenant_llm"
	TableTenantParams = "tenant_params"
	TableTenant       = "tenant"
	TableTenantPrompt = "tenant_prompt"
)

// AlterTableFields key: table name value : alter table script
var AlterTableFields = g.MapStrAny{
	"azure_llm_params": []string{
		`alter table azure_llm_params add column llm_type text  default 'aoai' null `,
	},
}

const ScriptTenantPrompt = `
 create table if not exists tenant_prompt
 (
    id        int auto_increment primary key,
    tenant_id varchar(255) not null,
    prompt    text         not  null,
    unique key tenant_prompt_tenant_id (tenant_id)
 ) 
`
const ScriptLLMParams = ` 
create table if not exists  llm_params
(
    id              int auto_increment primary key,  
    llm_name        varchar(255)       not null unique, 
    llm_type        varchar(100)       null , 
    description     text               null,
    base_url        varchar(500)       not null,
    resource_name   varchar(255)       not null,
    token           text               not null,
    embedding_model varchar(255)       not null,
    model           varchar(255)       not null,
    api_version     varchar(50)        not null,
    temperature     float default 0    not null,
    max_token       int   default 4096 not null
) 
`

const ScriptTenantLLM = `
	create table if not exists tenant_llm
(
    id        int auto_increment primary key,
    tenant_id varchar(255) not null,
    llm_name  varchar(255) not null,
    unique key tenant_llm_tenant_id_llm_name (tenant_id, llm_name)
);
`

const ScriptTenant = `
create table if not exists  tenant (
    tenant_Id NVARCHAR(200) NOT NULL,
    password NVARCHAR(20) NOT NULL,
    app_Key NVARCHAR(255) NULL,
    available BOOLEAN NOT NULL DEFAULT TRUE,
    remark TEXT NULL,
    PRIMARY KEY (tenant_Id)
);

`
const ScriptTenantParams = `
create table if not exists  tenant_params
(
    id        int auto_increment
        primary key,
    tenant_id varchar(200) not null,
    params    text         not null,
    constraint tenant_params_pk
        unique (tenant_id)
)
`

const (
	LLMParamsKeyPattern   = "llm_params:*"
	LLMParamsKeyFormat    = "llm_params:%s"
	TenantParamsKeyFormat = "tenant_params:%s"
	TenantLLMKeyPattern   = "tenant_llm:*"
	TenantLLMKeyFormat    = "tenant_llm:%s"
	TenantPromptKeyFormat = "tenant_prompt:%s"
	TenantInfoKeyFormat   = "tenant_info:%s"
)

var SensitiveWords = []string{
	"app_Key", "app_key", "password", "Password",
}

const XHeaderService = "X-SERVICE"
