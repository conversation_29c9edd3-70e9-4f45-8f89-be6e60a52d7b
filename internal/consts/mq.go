package consts

const (
	ExchangeName     = "dsh"
	RouteKeyMariaDB  = "mariadb.tenant"
	RouteKeyWeaviate = "weaviate.tenant"
)

// the action for mariadb
const (
	ActionInsert         = "insert"
	ActionDelete         = "delete"
	ActionUpdate         = "update"
	ActionUpdateOrInsert = "updateOrInsert"
	ActionCreateSchema   = "createSchema"
	ActionCreateTable    = "createTable"
)

// the action for weaviate
const (
	ActionCreateCollection  = "create_collection"
	ActionAddNewProperties  = "add_new_properties"
	ActionCreateData        = "create_data"
	ActionUpdateProperties  = "update_properties"
	ActionCreateTenant      = "create_tenant"
	ActionClearDataByFilter = "create_data_by_filter"
	ActionEmptyCollection   = "empty_collection"
	ActionDeleteCollection  = "delete_collection"
	ActionDeleteTenants     = "delete_tenants"
)
