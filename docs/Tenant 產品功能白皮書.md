# Tenant 產品功能白皮書

## 1. 產品概述

### 1.1 系統簡介

Tenant 是一個以微服務形式構建的租戶管理系統，採用 WEAVIATE 向量搜索引擎進行數據存儲，並透過 RabbitMQ 消息佇列處理數據操作請求。本系統專為多租戶環境設計，提供租戶管理、認證、授權以及與大型語言模型(LLM)整合的功能。系統基於 GoFrame 框架構建，支援分布式部署，為應用提供安全可靠的租戶身份管理服務。

### 1.2 應用場景

Tenant 系統適用於以下場景：
- 需要多租戶隔離的 SaaS 平台
- 需要統一身份認證的分布式系統
- 需要進行大語言模型資源分配的 AI 平台
- 需要精細權限控制的企業應用

## 2. 系統架構

### 2.1 技術架構

Tenant 系統採用以下技術架構：
- **編程語言**：Golang
- **框架**：GoFrame v2
- **數據存儲**：WEAVIATE 向量數據庫、Redis
- **消息佇列**：RabbitMQ
- **服務發現**：Nacos
- **配置管理**：Nacos
- **通訊協議**：HTTP REST API

### 2.2 系統組件

系統主要由以下組件構成：
- **API 層**：提供 HTTP REST API 接口，包括租戶管理、認證、授權等功能
- **控制層**：處理業務邏輯，實現 API 接口定義的功能
- **服務層**：提供核心業務服務，如租戶操作、認證、授權等
- **數據層**：負責數據存取，包括 Redis 緩存操作和 RabbitMQ 消息發送
- **訊息佇列**：RabbitMQ 負責轉發數據操作請求
- **數據服務處理層(DSH)**：處理實際的數據庫操作請求
- **通訊層**：負責微服務間的通訊

### 2.3 系統架構圖

```mermaid
flowchart TD
    A[API層] --> B[控制層]
    B --> C[服務層]
    C --> D[數據層]
    D --> G[訊息佇列\nRabbitMQ]
    G --> H["數據服務處理層\n(DSH服務)"]
    H --> E[(WEAVIATE\n向量數據庫)]
    D --> F[(Redis\n緩存)]
    C <--> I[通訊層]
    I <--> J[其他微服務]
    
    subgraph 系統架構
        A
        B
        C
        D
        I
    end
    
    subgraph 外部資源
        G
        H
        E
        F
        J
    end
```

### 2.4 認證流程圖

```mermaid
flowchart LR
    A[租戶] --> B{認證}
    B -- 成功 --> C[獲取令牌]
    C --> D[訪問服務]
    D --> E{權限檢查}
    E -- 允許 --> F[LLM服務]
    E -- 允許 --> G[其他服務]
    E -- 拒絕 --> H[返回錯誤]
    B -- 失敗 --> I[認證失敗]
```

### 2.5 數據操作流程圖

```mermaid
flowchart TD
    A[數據層請求] --> B{Redis緩存?}
    B -- 命中 --> C[返回數據]
    B -- 未命中 --> D[發送消息到RabbitMQ]
    D --> E[DSH服務接收消息]
    E --> F[執行數據庫操作]
    F --> G[返回結果]
    G --> H[更新Redis緩存]
    H --> C
```

## 3. 核心功能

### 3.1 租戶管理功能

#### 3.1.1 創建租戶
- **功能描述**：創建新的租戶記錄，包括租戶 ID 和密碼
- **接口路徑**：`/v1/tenants/create`
- **操作權限**：僅管理員可操作
- **輸入參數**：
  - `tenant_id`：租戶 ID，符合規範的字母數字下劃線
  - `password`：密碼，符合強密碼規範
- **輸出結果**：操作狀態碼和消息

#### 3.1.2 租戶列表
- **功能描述**：獲取所有租戶信息
- **接口路徑**：`/v1/tenants/list`
- **操作權限**：僅管理員可操作
- **輸出結果**：所有租戶的詳細信息列表

#### 3.1.3 更新租戶
- **功能描述**：更新租戶密碼
- **接口路徑**：`/v1/tenants/update`
- **操作權限**：管理員或租戶本身
- **輸入參數**：
  - `tenant_id`：租戶 ID
  - `old_password`：舊密碼
  - `new_password`：新密碼，符合強密碼規範
- **輸出結果**：操作狀態碼和消息

#### 3.1.4 刪除租戶
- **功能描述**：刪除指定租戶
- **接口路徑**：`/v1/tenants/remove`
- **操作權限**：僅管理員可操作
- **輸入參數**：
  - `tenant_id`：租戶 ID
  - `marked`：是否標記刪除，默認為 true
- **輸出結果**：操作狀態碼和消息

#### 3.1.5 租戶備註
- **功能描述**：為租戶添加備註說明
- **接口路徑**：`/v1/tenants/remark`
- **操作權限**：管理員
- **輸入參數**：
  - `tenant_id`：租戶 ID
  - `remark`：備註內容
- **輸出結果**：操作狀態碼和消息

#### 3.1.6 租戶啟用/停用
- **功能描述**：激活或停用租戶
- **接口路徑**：
  - 停用：`/v1/tenants/deactivateTenant`
  - 啟用：`/v1/tenants/activeTenant`
- **操作權限**：管理員
- **輸入參數**：
  - `tenant_id`：租戶 ID
- **輸出結果**：操作狀態碼和消息

#### 3.1.7 租戶參數設置
- **功能描述**：設置租戶特定參數
- **接口路徑**：
  - 設置：`/v1/tenants/setTenantsParams`
  - 獲取：`/v1/tenants/getTenantsParams`
- **操作權限**：管理員
- **輸入參數**：
  - `tenant_id`：租戶 ID
  - `params`：參數 JSON 對象（僅設置時需要）
- **輸出結果**：操作狀態碼和消息，獲取時返回參數數據

### 3.2 認證與授權功能

#### 3.2.1 生成 APP Key
- **功能描述**：為租戶生成新的 APP Key
- **接口路徑**：`/v1/tenants/genkey`
- **操作權限**：管理員或租戶本身（需提供密碼）
- **輸入參數**：
  - `tenant_id`：租戶 ID
  - `password`：密碼（可選，非管理員必須）
- **輸出結果**：新生成的 APP Key、操作狀態碼和消息

#### 3.2.2 獲取令牌
- **功能描述**：獲取訪問令牌和刷新令牌
- **接口路徑**：`/v1/tenants/gettokens`
- **輸入參數**：
  - `tenant_id`：租戶 ID
  - `password`：密碼
  - `app_key`：APP Key
- **輸出結果**：
  - `access_token`：訪問令牌
  - `refresh_token`：刷新令牌
  - 操作狀態碼和消息

#### 3.2.3 刷新令牌
- **功能描述**：使用刷新令牌獲取新的訪問令牌
- **接口路徑**：`/v1/tenants/refreshtoken`
- **輸入參數**：
  - `refresh_token`：刷新令牌
- **輸出結果**：
  - `access_token`：新的訪問令牌
  - 操作狀態碼和消息

#### 3.2.4 租戶認證
- **功能描述**：驗證租戶對特定服務和操作的訪問權限
- **接口路徑**：`/v1/tenants/auth`
- **輸入參數**：
  - `service`：服務名稱
  - `action`：操作名稱
- **輸出結果**：認證結果

### 3.3 大語言模型(LLM)管理功能

#### 3.3.1 獲取 LLM 列表
- **功能描述**：獲取所有可用的 LLM 配置
- **接口路徑**：`/v1/tenants/getLLMs`
- **操作權限**：管理員
- **輸入參數**：
  - `detail`：是否返回詳細信息
- **輸出結果**：LLM 配置列表

#### 3.3.2 獲取租戶可用 LLM
- **功能描述**：獲取特定租戶可使用的 LLM
- **接口路徑**：`/v1/tenants/getLLMByTenant`
- **操作權限**：管理員或租戶本身
- **輸入參數**：
  - `tenant_id`：租戶 ID
- **輸出結果**：該租戶可用的 LLM 配置列表

#### 3.3.3 分配 LLM 給租戶
- **功能描述**：為租戶分配使用特定 LLM 的權限
- **接口路徑**：`/v1/tenants/assignLLMToTenant`
- **操作權限**：管理員
- **輸入參數**：
  - `tenant_id`：租戶 ID
  - `llms`：LLM 名稱數組
- **輸出結果**：操作狀態碼和消息

#### 3.3.4 移除租戶的 LLM 訪問權限
- **功能描述**：移除租戶使用特定 LLM 的權限
- **接口路徑**：`/v1/tenants/removeTenantLLM`
- **操作權限**：管理員
- **輸入參數**：
  - `tenant_id`：租戶 ID
  - `llms`：LLM 名稱數組
- **輸出結果**：操作狀態碼和消息

#### 3.3.5 獲取 LLM-租戶關係列表
- **功能描述**：獲取所有 LLM 與租戶的關聯關係
- **接口路徑**：`/v1/tenants/getLLMTenantList`
- **操作權限**：管理員
- **輸出結果**：LLM 與租戶關聯數據

## 4. 數據模型

### 4.1 租戶模型 (TenantInfo)
- `TenantID`: 租戶唯一標識符
- `Password`: 租戶密碼
- `APPKey`: 應用密鑰
- `Available`: 租戶是否可用
- `Remark`: 租戶備註

### 4.2 LLM 配置模型 (LLMConfiguration)
- `LLMName`: LLM 名稱
- `Description`: 描述
- `BaseUrl`: 基礎 URL
- `Token`: 訪問令牌
- `ResourceName`: 資源名稱
- `EmbeddingModel`: 嵌入模型
- `ModelId`: 模型 ID
- `APIVersion`: API 版本
- `Temperature`: 溫度參數
- `MaxToken`: 最大令牌數
- `LLMType`: LLM 類型

### 4.3 令牌聲明模型 (Claims)
- `TenantID`: 租戶 ID
- `Password`: 密碼
- `AppKey`: 應用密鑰
- `RegisteredClaims`: JWT 標準聲明

### 4.4 RabbitMQ 消息模型
- `RouteKey`: 路由鍵
- `Action`: 操作類型
- `Data`: 消息數據
- `Type`: 消息類型

### 4.5 實體關係圖

```mermaid
erDiagram
    TenantInfo ||--o{ LLMConfiguration : "uses"
    LLMConfiguration }|..|{ RabbitMQMessage : "transported-by"
    TenantInfo }|..|{ RabbitMQMessage : "managed-via"
    RabbitMQMessage }|--|| DSHService : "processed-by"
    
    TenantInfo {
        string TenantID
        string Password
        string APPKey
        boolean Available
        string Remark
    }
    LLMConfiguration {
        string LLMName
        string Description
        string BaseUrl
        string Token
        string ResourceName
        string EmbeddingModel
        string ModelId
        string APIVersion
        float Temperature
        int MaxToken
        string LLMType
    }
    RabbitMQMessage {
        string RouteKey
        string Action
        bytes Data
        string Type
    }
    DSHService {
        string Schema
        string Table
        string Fields
        string WhereConditions
    }
```

## 5. 系統安全

### 5.1 認證機制
系統採用基於 JWT 的認證機制：
- 使用訪問令牌和刷新令牌分離的模式
- 訪問令牌有效期默認為 1 天
- 刷新令牌有效期默認為 7 天
- 令牌使用 HS256 算法簽名

### 5.2 密碼策略
系統實施強密碼策略：
- 密碼至少 8 位長度
- 必須包含字母、數字和特殊字符
- 定期更換密碼機制

### 5.3 數據安全
- 敏感信息（密碼、APP Key）在日誌中自動屏蔽
- 數據通過 WEAVIATE 向量數據庫存儲，支持數據加密
- Redis 用於臨時數據快取，提高系統性能
- RabbitMQ 用於異步處理數據操作，增強系統穩定性

### 5.4 認證流程序列圖

```mermaid
sequenceDiagram
    participant 客戶端
    participant API層
    participant 控制層
    participant 服務層
    participant 數據層
    participant RabbitMQ
    participant DSH服務
    participant 數據庫
    
    客戶端->>API層: 請求認證
    API層->>控制層: 傳遞請求
    控制層->>服務層: 認證請求
    服務層->>數據層: 查詢租戶信息
    數據層->>Redis: 查詢緩存
    alt 缓存未命中
        數據層->>RabbitMQ: 發送數據查詢消息
        RabbitMQ->>DSH服務: 轉發消息
        DSH服務->>數據庫: 執行查詢
        數據庫-->>DSH服務: 返回數據
        DSH服務-->>RabbitMQ: 返回結果
        RabbitMQ-->>數據層: 返回結果
        數據層->>Redis: 更新緩存
    end
    數據層-->>服務層: 返回租戶信息
    服務層-->>控制層: 生成令牌
    控制層-->>API層: 返回令牌
    API層-->>客戶端: 認證成功
```

## 6. 部署與維護

### 6.1 系統要求
- Golang 1.16+
- Nacos 服務發現與配置中心
- WEAVIATE 向量數據庫
- Redis 緩存服務
- RabbitMQ 消息佇列服務
- DSH 數據服務處理服務

### 6.2 配置項
主要配置項包括：
- Nacos 連接配置
- 系統管理員帳號和密碼
- 令牌有效期設置
- LLM 檢查間隔時間
- 系統密鑰
- RabbitMQ 連接配置
- DSH 服務配置

### 6.3 日誌管理
系統支持多種日誌級別：
- ERROR: 錯誤信息
- INFO: 一般信息
- DEBUG: 調試信息
- RR: 請求響應信息

## 7. 故障排除

### 7.1 常見錯誤碼
- `0`: 成功
- `-1`: 系統錯誤
- `1000`: 相同租戶 ID 已存在
- `1001`: 租戶 ID 格式不符合規範
- `1002`: 密碼不符合強密碼規範
- `1003`: 租戶不存在
- `1004`: 密碼錯誤
- `1005`: APP Key 錯誤
- `1006`: 令牌已過期

### 7.2 問題診斷方法
- 檢查系統日誌
- 驗證 Nacos 服務狀態
- 確認 WEAVIATE 數據庫連接
- 檢查 Redis 緩存服務
- 驗證 RabbitMQ 服務狀態
- 檢查 DSH 服務運行狀態
- 驗證 JWT 令牌有效性

## 8. API 調用示例

### 8.1 創建租戶
```json
POST /v1/tenants/create
{
  "tenant_id": "example_tenant",
  "password": "StrongP@ss123"
}
```

### 8.2 獲取令牌
```json
POST /v1/tenants/gettokens
{
  "tenant_id": "example_tenant",
  "password": "StrongP@ss123",
  "app_key": "generated_app_key"
}
```

### 8.3 為租戶分配 LLM
```json
POST /v1/tenants/assignLLMToTenant
{
  "tenant_id": "example_tenant",
  "llms": ["gpt-4", "claude-3"]
}
```

## 9. 版本歷史

### 當前版本
Tenants 系統目前處於初始開發階段，提供基本的租戶管理、認證和 LLM 分配功能。

### 計劃功能
- 用戶界面管理控制台
- 更細粒度的權限控制
- 多因素認證
- 審計日誌系統
- API 使用統計和限流
- RabbitMQ 集群部署支持
- DSH 服務水平擴展 