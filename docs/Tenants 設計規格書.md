# Tenants 設計規格書

## 1. 概述

### 1.1 系統介紹
Tenants 是一個基於 GoFrame 框架構建的微服務，用於管理租戶信息及其與大型語言模型(LLM)的關聯關係。系統採用 WEAVIATE 向量搜索引擎進行數據存儲，並使用 RabbitMQ 消息佇列處理數據操作請求，以實現數據操作的異步處理和系統的高可用性。

### 1.2 設計目標
- 提供租戶創建、管理和認證的功能
- 支持租戶與LLM的關聯管理
- 實現高效的數據存取和緩存機制
- 支持分布式部署和微服務架構
- 確保系統安全性和可擴展性

### 1.3 目標讀者
本文檔面向軟體開發工程師和系統維護人員，旨在提供系統的詳細設計和實現細節，以便於開發、維護和擴展系統功能。

## 2. 系統架構

### 2.1 整體架構
Tenants 系統採用微服務架構，主要包括以下幾個層次：
- **API 層**：提供 HTTP API 接口，處理外部請求
- **控制層**：實現業務邏輯，處理請求參數和響應格式
- **服務層**：提供核心業務功能，包括租戶管理、認證和 LLM 關聯管理
- **數據層**：處理數據存取，包括 Redis 緩存和通過 RabbitMQ 發送消息到數據服務
- **消息佇列**：使用 RabbitMQ 處理異步數據操作請求
- **數據服務處理層**：處理實際的數據庫操作
- **數據存儲**：包括 WEAVIATE 向量數據庫和 Redis 緩存

### 2.2 模塊組成

#### 2.2.1 核心接口和實現類

```mermaid
classDiagram
    class ITenant {
        <<interface>>
        +GetTenantAvailable(ctx, tenantID) bool, error
        +NewTokens(ctx, tenantID, password, appKey) string, string, error
        +CreateTenant(ctx, tenantID, password) error
        +DisableTenant(ctx, tenantID, marked) error
        +ActivateTenant(ctx, tenantID) error
        +Remark(ctx, tenantId, remark) error
        +UpdateTenant(ctx, tenantID, oldPassword, newPassword, isAdmin) error
        +ListTenants(ctx) *gjson.Json, error
        +GenNewAppKeyForTenant(ctx, tenantID, password, isAdmin) error, string
        +RefreshToken(ctx, refreshToken) string, error
        +AuthTenant(ctx, token) error
    }
    
    class sTenant {
        -dataCache *gcache.Cache
        -client *gclient.Client
        +setTenantInfoInCache(ctx, ti) error
        +removeFromCache(ctx, tenantID) error
        +getTenantInfoByIDFromCache(ctx, tenantID) *TenantInfo, error
        +checkTenantInfoInCache(ctx, tenantID) bool, error
        +checkToken(ctx, claims) bool, error
        +parseToken(ctx, token) *Claims, error
        +createTokens(ctx, tenantID, password, appkey, tokenType) string, error
        +sendMessage(ctx, action, data) error
        +sendToDSH(ctx, in) *GetContentsRes, error
        +validateTenantPass(ctx, tenantID, password, checkOldPass) error, map
        +tenantIsExisted(ctx, tenantID, status) bool, *gjson.Json, error
        +deleteTenant(ctx, tenantID) error
        +updateTenant(ctx, tenantID, properties) error
    }
    
    class IDataStore {
        <<interface>>
        +WriteLLMParams(ctx, llmParams) error
        +RemoveLLMParams(ctx, llmName) error
        +AssignLLMToTenant(ctx, tenantId, llmName) error
        +RemoveTenantLLM(ctx, tenantId, llms) error
        +GetLLMs(ctx, detail) *gjson.Json
        +GetTenantLLM(ctx, tenantId) *gjson.Json
        +GetLLMTenantsList(ctx) []*gjson.Json
        +SetTenantParams(ctx, tenantId, params) error
        +GetTenantParams(ctx, tenantId) string, error
    }
    
    class sDataStore {
        -client *gclient.Client
        +sendMessage(ctx, action, data) error
        +getLLMNameFromCache(ctx) []string
        +checkProfile(ctx) void
        +sendToDSH(ctx, in) *GetContentsRes, error
        +loadTenantLLMToCache(ctx) void
        +checkDatabase(ctx) void
        +checkCreateTable(ctx) void
        +isLLMExist(ctx, llmName) error
        +getLLMParamsByName(ctx, llmName) *LLMConfiguration
        +setLLMTenantToCache(ctx, tenantId, llmName) void
    }
    
    class IMessageQ {
        <<interface>>
        +Send(ctx, routeKey, action, data) error
    }
    
    class sMessageQ {
        -conn *amqp.Connection
        -channel *amqp.Channel
        +logger() glog.ILogger
    }
    
    ITenant <|.. sTenant
    IDataStore <|.. sDataStore
    IMessageQ <|.. sMessageQ
    sTenant --> IDataStore : uses
    sTenant --> IMessageQ : uses
    sDataStore --> IMessageQ : uses
```

## 3. 啟動流程

### 3.1 系統初始化和服務啟動

```mermaid
flowchart TD
    subgraph Boot["啟動流程"]
        A[main.go] --> B["boot.initNacos()"]
        B --> C["設置服務註冊"]
        B --> D["設置配置適配器"]
        A --> E["cmd.Main.Run"]
    end
    
    subgraph HTTP["HTTP服務"]
        E --> F["g.Server(service.GetSvcName())"]
        F --> G["設置路由組"]
        G --> H["綁定控制器"]
        G --> I["添加中間件"]
        I --> J["MiddleHandler - 請求/響應日誌"]
    end
    
    subgraph Services["服務初始化"]
        K["service.RegisterTenant(tenant.New())"]
        L["service.RegisterDataStore(datastore.New())"]
        M["service.RegisterMessageQ(messageQ.New())"]
    end
    
    subgraph Cache["緩存管理"]
        N["Redis緩存租戶信息"]
        O["Redis緩存LLM配置"]
        P["Redis緩存租戶LLM關聯"]
    end
    
    subgraph DSH["數據服務處理"]
        Q["RabbitMQ消息處理"]
        R["數據庫操作執行"]
        S["結果返回"]
    end
    
    A -.-> K
    A -.-> L
    A -.-> M
    K --> N
    L --> O
    L --> P
    I --> Q
    Q --> R
    R --> S
```

## 4. 核心功能模塊設計

### 4.1 租戶管理模塊

#### 4.1.1 租戶創建流程

```mermaid
sequenceDiagram
    participant Client
    participant ControllerV1
    participant sTenant
    participant sDataStore
    participant sMessageQ
    participant Redis
    participant RabbitMQ
    participant DSH
    participant Weaviate
    
    Client->>ControllerV1: CreateTenant(tenantID, password)
    ControllerV1->>ControllerV1: isAdmin(ctx)
    ControllerV1->>ControllerV1: validate format
    ControllerV1->>sTenant: CreateTenant(tenantID, password)
    sTenant->>sTenant: tenantIsExisted(tenantID)
    sTenant->>sDataStore: via DSH service
    sDataStore->>sMessageQ: Send(RouteKeyMariaDB, ActionCreateSchema, data)
    sMessageQ->>RabbitMQ: Publish(message)
    RabbitMQ->>DSH: Process message
    DSH->>Weaviate: Create tenant
    Weaviate-->>DSH: Response
    DSH-->>RabbitMQ: Response
    RabbitMQ-->>sMessageQ: Response
    sMessageQ-->>sDataStore: Response
    sDataStore-->>sTenant: Response
    sTenant->>Redis: Set cache
    sTenant-->>ControllerV1: Response
    ControllerV1-->>Client: Response
```

### 4.2 認證與令牌管理模塊

#### 4.2.1 令牌生成和刷新流程

```mermaid
flowchart LR
    subgraph TokenManagement["令牌管理流程"]
        A[客戶端] --> B["ControllerV1.NewTokens()"]
        B --> C["service.Tenant().NewTokens()"]
        C --> D{"檢查緩存中\n租戶信息"}
        D -- "存在" --> E{"驗證租戶\n密碼和AppKey"}
        D -- "不存在" --> F["查詢租戶信息"]
        F --> E
        E -- "驗證成功" --> G["生成訪問令牌\n和刷新令牌"]
        E -- "驗證失敗" --> H["返回錯誤"]
        G --> I["返回令牌"]
        
        J[客戶端] --> K["ControllerV1.RefreshToken()"]
        K --> L["service.Tenant().RefreshToken()"]
        L --> M{"解析刷新令牌"}
        M -- "解析成功" --> N{"驗證令牌是否過期"}
        M -- "解析失敗" --> O["返回錯誤"]
        N -- "未過期" --> P["生成新的訪問令牌"]
        N -- "已過期" --> Q["返回錯誤"]
        P --> R["返回新訪問令牌"]
    end
```

### 4.3 LLM 管理模塊

#### 4.3.1 LLM 配置管理流程

```mermaid
flowchart TD
    subgraph LLMManagement["LLM 管理流程"]
        A["checkProfile() 定時任務"] --> B{"讀取配置中的LLM"}
        B --> C{"與緩存中的LLM比較"}
        C -- "LLM已存在但配置變更" --> D["更新Redis緩存"]
        C -- "LLM不存在" --> E["添加到Redis緩存"]
        C -- "緩存中存在但配置中刪除" --> F["從Redis緩存刪除"]
        D --> G["通過消息更新數據庫"]
        E --> G
        F --> H["通過消息從數據庫刪除"]
        
        I["AssignLLMToTenant()"] --> J{"檢查LLM是否存在"}
        J -- "存在" --> K["添加租戶LLM關聯"]
        J -- "不存在" --> L["返回錯誤"]
        K --> M["更新Redis緩存"]
        
        N["RemoveTenantLLM()"] --> O["刪除租戶LLM關聯"]
        O --> P["更新Redis緩存"]
        
        Q["GetTenantLLM()"] --> R{"檢查緩存"}
        R -- "緩存存在" --> S["返回緩存數據"]
        R -- "緩存不存在" --> T["查詢數據庫"]
        T --> U["更新緩存並返回"]
    end
```

## 5. 數據模型

### 5.1 核心數據結構

#### 5.1.1 租戶信息 (TenantInfo)
```go
type TenantInfo struct {
    TenantID  string `json:"tenant_Id"`
    Password  string `json:"password"`
    APPKey    string `json:"app_Key"`
    Available bool   `json:"available"`
    Remark    string `json:"remark"`
}
```

#### 5.1.2 LLM 配置 (LLMConfiguration)
```go
type LLMConfiguration struct {
    LLMName        string  `json:"llm_name"`
    Description    string  `json:"description"`
    BaseUrl        string  `json:"base_url"`
    Token          string  `json:"token"`
    ResourceName   string  `json:"resource_name"`
    EmbeddingModel string  `json:"embedding_model"`
    ModelId        string  `json:"model"`
    APIVersion     string  `json:"api_version"`
    Temperature    float32 `json:"temperature"`
    MaxToken       int     `json:"max_token"`
    LLMType        string  `json:"llm_type"`
}
```

#### 5.1.3 令牌聲明 (Claims)
```go
type Claims struct {
    TenantID string `json:"tenant_id"`
    Password string `json:"password"`
    AppKey   string `json:"app_Key"`

    jwt.RegisteredClaims
}
```

#### 5.1.4 消息佇列消息 (MQMessage)
```go
type MQMessage struct {
    Schema          string      `json:"schema"`
    Table           string      `json:"table"`
    WhereConditions string      `json:"where"`
    WhereParams     []any       `json:"params"`
    Data            any         `json:"data"`
}
```

### 5.2 數據流轉
系統數據主要通過以下幾種方式進行流轉：
1. Redis 緩存：存儲租戶信息、LLM配置和租戶-LLM關聯
2. RabbitMQ 消息：數據操作請求從服務層發送到數據服務處理層
3. WEAVIATE 數據庫：存儲所有持久化數據

#### 5.2.1 數據流轉圖示

```mermaid
flowchart TD
    subgraph DataFlow["數據流轉"]
        A[API請求] --> B[控制器層]
        B --> C[服務層]
        
        subgraph CacheOps["緩存操作"]
            D[檢查Redis緩存]
            E[更新Redis緩存]
        end
        
        subgraph MsgQueue["消息佇列"]
            F[構建消息]
            G[發送至RabbitMQ]
        end
        
        subgraph DSHService["DSH服務"]
            H[接收並處理消息]
            I[執行數據庫操作]
            J[返回操作結果]
        end
        
        C --> D
        D -- "緩存命中" --> M[返回結果]
        D -- "緩存未命中" --> F
        C -- "更新操作" --> F
        F --> G
        G --> H
        H --> I
        I --> J
        J -- "通過RabbitMQ" --> K[接收操作結果]
        K --> E
        E --> M
        M --> B
        B --> N[API響應]
    end
```

## 6. 接口設計

### 6.1 API 接口列表

| 接口路徑 | 方法 | 描述 | 參數 |
|---------|------|------|------|
| /v1/tenants/create | POST | 創建新租戶 | tenant_id, password |
| /v1/tenants/list | POST | 獲取租戶列表 | 無 |
| /v1/tenants/update | POST | 更新租戶密碼 | tenant_id, old_password, new_password |
| /v1/tenants/remove | POST | 刪除租戶 | tenant_id, marked |
| /v1/tenants/genkey | POST | 生成新的 APP Key | tenant_id, password |
| /v1/tenants/auth | POST | 驗證租戶權限 | service, action |
| /v1/tenants/gettokens | POST | 獲取訪問令牌和刷新令牌 | tenant_id, password, app_key |
| /v1/tenants/refreshtoken | POST | 刷新訪問令牌 | refresh_token |
| /v1/tenants/getLLMs | POST | 獲取所有 LLM | detail |
| /v1/tenants/getLLMByTenant | POST | 獲取租戶可用的 LLM | tenant_id |
| /v1/tenants/assignLLMToTenant | POST | 分配 LLM 給租戶 | tenant_id, llms |
| /v1/tenants/removeTenantLLM | POST | 移除租戶的 LLM | tenant_id, llms |
| /v1/tenants/getLLMTenantList | POST | 獲取 LLM-租戶關聯列表 | 無 |
| /v1/tenants/deactivateTenant | POST | 停用租戶 | tenant_id |
| /v1/tenants/activeTenant | POST | 啟用租戶 | tenant_id |
| /v1/tenants/remark | POST | 為租戶添加備註 | tenant_id, remark |
| /v1/tenants/setTenantsParams | POST | 設置租戶參數 | tenant_id, params |
| /v1/tenants/getTenantsParams | POST | 獲取租戶參數 | tenant_id |

## 7. 緩存策略

### 7.1 Redis 緩存機制
系統使用 Redis 緩存以下數據：
1. 租戶信息：使用租戶 ID 作為鍵
2. LLM 配置：使用格式化的鍵 `llm_params:{llm_name}`
3. 租戶-LLM 關聯：使用格式化的鍵 `tenant_llm:{tenant_id}`
4. 租戶參數：使用格式化的鍵 `tenant_params:{tenant_id}`

### 7.2 緩存更新策略
- LLM 配置：通過定時任務檢查配置變更
- 租戶信息：在修改租戶數據時同步更新
- 租戶-LLM 關聯：在分配或移除 LLM 時更新
- 租戶參數：在設置參數時更新

## 8. 安全機制

### 8.1 認證機制
系統使用 JWT 令牌進行認證，包括：
- 訪問令牌：有效期默認為 1 天
- 刷新令牌：有效期默認為 7 天
- 令牌內容包含租戶 ID、密碼和 APP Key

### 8.2 密碼策略
系統要求密碼滿足以下條件：
- 至少 8 位長度
- 包含字母、數字和特殊字符

### 8.3 敏感數據處理
- 敏感數據（密碼、APP Key）在日誌中自動屏蔽
- 令牌簽名使用系統配置的密鑰

## 9. 錯誤處理

### 9.1 錯誤碼定義

| 錯誤碼 | 描述 |
|--------|------|
| 0 | 成功 |
| -1 | 系統錯誤 |
| 1000 | 相同租戶 ID 已存在 |
| 1001 | 租戶 ID 格式不符合規範 |
| 1002 | 密碼不符合強密碼規範 |
| 1003 | 租戶不存在 |
| 1004 | 密碼錯誤 |
| 1005 | APP Key 錯誤 |
| 1006 | 令牌已過期 |

### 9.2 錯誤處理機制
系統使用 `gerror` 包處理錯誤，每個錯誤都關聯一個錯誤碼。控制器層會捕獲服務層返回的錯誤，並將其轉換為適當的 HTTP 響應。

## 10. 配置項

### 10.1 系統配置
- `nacos.*`：Nacos 服務發現和配置中心配置
- `system.*`：系統基本配置
  - `admin_account`：管理員賬號
  - `admin_pass`：管理員密碼
  - `access_token_expire`：訪問令牌有效期
  - `refresh_token_expire`：刷新令牌有效期
  - `secret_key`：令牌簽名密鑰
  - `issuer`：令牌簽發者
  - `data_service.*`：數據服務配置

### 10.2 RabbitMQ 配置
- `rabbitMQ.url`：RabbitMQ 連接 URL
- `system.delay_init_rabbitmq`：RabbitMQ 初始化延遲
- `system.interval_init_rabbitmq`：RabbitMQ 初始化間隔

### 10.3 LLM 配置
- `llms`：LLM 配置列表
- `llm.check_llms_interval`：LLM 檢查間隔

## 11. 部署指南

### 11.1 系統要求
- Golang 1.16+
- Nacos 服務發現與配置中心
- WEAVIATE 向量數據庫
- Redis 緩存服務
- RabbitMQ 消息佇列服務
- DSH 數據服務處理服務

### 11.2 部署步驟
1. 確保所有依賴服務已正確配置並運行
2. 編譯服務：`go build -o tenants main.go`
3. 準備配置文件（通過 Nacos 或本地配置）
4. 啟動服務：`./tenants`

### 11.3 環境配置建議
- 生產環境建議部署多個服務實例，通過 Nacos 進行服務發現
- Redis 建議配置主從複製或叢集模式
- RabbitMQ 建議配置集群以提高可用性
- WEAVIATE 建議配置適當的資源以滿足數據量要求

## 12. 開發指南

### 12.1 添加新功能
1. 在 `api/tenants/v1/tenants.go` 中定義新的請求和響應結構
2. 在 `api/tenants/tenants.go` 的 `ITenantsV1` 接口中添加新方法
3. 在 `internal/controller/tenants/tenants_v1_teanants.go` 中實現控制器方法
4. 如需添加新的服務功能，在相應的服務接口和實現中添加

### 12.2 修改現有功能
1. 確保了解現有代碼的數據流和緩存機制
2. 修改時需同步更新緩存和數據庫
3. 保持錯誤處理的一致性

### 12.3 調試技巧
- 使用 `g.Log().Cat(consts.DEBUG).Debug()` 輸出調試信息
- 通過 Redis 客戶端檢查緩存數據
- 通過 RabbitMQ 管理界面監控消息流轉
- 確保測試覆蓋各種錯誤情況

## 13. 維護指南

### 13.1 日誌分析
系統日誌包含以下類別：
- `error`：錯誤信息
- `info`：一般信息
- `debug`：調試信息
- `rr`：請求響應信息

### 13.2 性能監控
- 監控 Redis 緩存命中率
- 監控 RabbitMQ 消息處理速度和積壓情況
- 監控 API 響應時間

### 13.3 常見問題排查
- 認證失敗：檢查令牌配置和有效期
- 數據不一致：檢查緩存更新邏輯
- 性能問題：檢查數據庫查詢和緩存使用

## 14. 未來擴展

### 14.1 計劃功能
- 用戶界面管理控制台
- 更細粒度的權限控制
- 多因素認證
- 審計日誌系統
- API 使用統計和限流
- RabbitMQ 集群部署支持
- DSH 服務水平擴展

### 14.2 擴展建議
- 考慮添加更多的緩存策略以提高性能
- 實現更完善的監控和告警機制
- 增強安全性措施，如 IP 白名單、請求限流等 

## 15. 系統狀態轉換

### 15.1 LLM 和租戶狀態轉換

以下狀態圖展示了系統中 LLM 和租戶的狀態轉換關係：

```mermaid
stateDiagram-v2
    [*] --> 待創建: 配置中定義LLM
    待創建 --> 活動: 檢查Profile
    活動 --> 停用: 從配置中移除
    活動 --> 更新: 配置變更
    更新 --> 活動: 更新完成
    停用 --> [*]: 刪除緩存和數據庫記錄
    
    state 活動 {
        [*] --> 未分配: 初始狀態
        未分配 --> 已分配: AssignLLMToTenant
        已分配 --> 未分配: RemoveTenantLLM
    }

    state 租戶狀態 {
        [*] --> 活躍: CreateTenant
        活躍 --> 停用: DisableTenant
        停用 --> 活躍: ActivateTenant
    }
```

### 15.2 狀態轉換說明

1. **LLM 生命週期**:
   - LLM 首先在配置中定義，處於待創建狀態
   - 通過 `checkProfile()` 定時任務檢測到並創建到緩存和數據庫中，進入活動狀態
   - 配置變更時進入更新狀態，更新完成後返回活動狀態
   - 從配置中移除時進入停用狀態，最終從系統中刪除

2. **LLM 分配狀態**:
   - LLM 初始為未分配狀態
   - 通過 `AssignLLMToTenant` 分配給租戶後變為已分配狀態
   - 通過 `RemoveTenantLLM` 移除租戶關聯後返回未分配狀態

3. **租戶狀態**:
   - 租戶通過 `CreateTenant` 創建後處於活躍狀態
   - 通過 `DisableTenant` 可將租戶停用
   - 通過 `ActivateTenant` 可重新啟用已停用的租戶 