package utility

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/gogf/gf/v2/container/garray"

	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/text/gstr"
)

func IsStrongPassword(password string) bool {
	// 至少包含一个数字
	reDigit := regexp.MustCompile(`\d`)
	if !reDigit.MatchString(password) {
		return false
	}

	// 至少包含一个小写字母
	reLower := regexp.MustCompile(`[a-z]`)
	if !reLower.MatchString(password) {
		return false
	}

	// 至少包含一个大写字母
	reUpper := regexp.MustCompile(`[A-Z]`)
	if !reUpper.MatchString(password) {
		return false
	}

	// 至少包含一个特殊字符
	reSpecial := regexp.MustCompile(`[^\w]`)
	if !reSpecial.MatchString(password) {
		return false
	}

	// 长度在8到16之间
	if len(password) < 8 || len(password) > 16 {
		return false
	}

	return true
}

func IsValidTenantName(name string) bool {
	// 正则表达式模式
	pattern := `^[a-zA-Z0-9_-]{4,64}$`

	// 编译正则表达式
	re := regexp.MustCompile(pattern)

	// 使用正则表达式进行匹配
	return re.MatchString(name) && containsLettersAndNumbers(name)
}

func containsLettersAndNumbers(s string) bool {
	hasLetter := false
	hasNumber := false

	for _, char := range s {
		if (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') {
			hasLetter = true
		} else if char >= '0' && char <= '9' {
			hasNumber = true
		}
	}

	return hasLetter && hasNumber
}

func HideStr(data *gjson.Json, keys []string, percentage int) string {
	if data == nil {
		return ""
	}
	str := data.MustToJsonIndentString()
	strs := gstr.SplitAndTrim(str, "\n", "\t")
	sb := strings.Builder{}
	for _, s := range strs {
		keyValue := gstr.Split(s, ":")
		if len(keyValue) == 2 {
			k := gstr.TrimAll(keyValue[0], `"`)
			t := gstr.TrimAll(keyValue[1], `"`)
			haveComma := gstr.HasSuffix(t, ",")
			v := gstr.TrimRightStr(t, ",")

			if garray.NewStrArrayFrom(keys).Contains(k) {
				nv := fmt.Sprintf("%q:%q", k, gstr.HideStr(v, percentage, "*"))
				if haveComma {
					nv += ","
				}
				sb.WriteString(nv)
				continue
			}
		}

		sb.WriteString(s)
	}

	return sb.String()
}
