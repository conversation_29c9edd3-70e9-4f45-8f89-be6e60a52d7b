server:
 address: "*************:8084,************:8084"
 swaggerPath: "/swagger"
 openapiPath: "/api.json" 
logger:
  path: "./logs"
  file:  "tenants_{Y-m-d}.log"
  rotateSize: "10M"
  rotateBackupLimit: 3 
  rotateBackupExpire: "3d"
  rotateBackupCompress: 9
  level: "dev"
  stdout: true
weaviate:
 host: localhost:8070
 scheme: http
 recreate_class_onstart: false
system:
 keep_cache_duration: "1d"
 access_token_expire: "1d"
 refresh_token_expire: "7d"
 issuer: "Ai3"
 secret_key: "seckey"
 admin_account: "admin"
 admin_pass: "a~1d@3m$"
 update_services_duration: "1m"
services:
  -  service_name: "aile-gateway"
     key: "c0e2a635-a820-4b0b-ad75-827d1dfd543a"
     include:
       -  micro_service: "quizto"
          actions: ["chat"] 
permissions:
  exclude:
   quizto: ["deleteall"]

llm:
  token: "050928bf5c1b47e5b3a2da43b164f383"
  resourceName: "qbibotopenai"
  deploymentId: "text-embedding-ada-002"



