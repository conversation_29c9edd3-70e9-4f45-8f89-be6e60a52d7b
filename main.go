package main

import (
	"context"

	_ "github.com/gogf/gf/contrib/drivers/mysql/v2"
	_ "github.com/gogf/gf/contrib/nosql/redis/v2"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gbuild"
	"github.com/gogf/gf/v2/os/gctx"
	"github.com/gogf/gf/v2/os/glog"
	_ "tenants/boot"
	_ "tenants/internal/logic"
	_ "tenants/internal/packed"

	"tenants/internal/cmd"
)

func main() {
	g.Log().SetHeaderPrint(false)
	buildInfo := gbuild.Info()
	g.Log().Infof(context.TODO(), "Tenants Version: %s , Build Time: %s", buildInfo.Version, buildInfo.Time)
	g.Log().SetHeaderPrint(true)
	g.Log().SetFlags(glog.F_FILE_SHORT | glog.F_TIME_STD | glog.F_CALLER_FN)
	cmd.Main.Run(gctx.GetInitCtx())
}
