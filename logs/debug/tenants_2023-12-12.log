2023-12-12 09:25:20.120 [DEBU] {e824994723f19f176df4e61cec727d18} Get all tenant info from cache
2023-12-12 09:25:20.133 [DEBU] {e824994723f19f176df4e61cec727d18} &{TenantID:Tenant04 Password:1qaz@WSX3edc APPKey:36a8bc40-08b9-4944-a6f5-0f4b9037ebbd Available:true}
2023-12-12 09:25:20.133 [DEBU] {e824994723f19f176df4e61cec727d18} &{TenantID:GPT-20230704-0002 Password:1qaz@WSX3edc APPKey:4bf262c1-0f66-4641-a0fa-f2da216a1954 Available:true}
2023-12-12 09:25:20.133 [DEBU] {e824994723f19f176df4e61cec727d18} &{TenantID:R20230704-0001 Password:!QAZ2wsx APPKey:9473a321-a079-4cda-8019-00197512ef89 Available:true}
2023-12-12 09:25:20.133 [DEBU] {e824994723f19f176df4e61cec727d18} &{TenantID:GPT-20230704-0003 Password:!QAZ2wsx APPKey:e4607d5c-f2e1-4158-ae67-e7e611b37398 Available:true}
2023-12-12 09:25:20.133 [DEBU] {e824994723f19f176df4e61cec727d18} &{TenantID:GPT-20230704-0001 Password:!QAZ2wsx APPKey:8e7befff-0ec3-4894-ad90-52869be71e83 Available:true}
2023-12-12 09:27:00.443 [DEBU] {987bad143bf19f170fcdce6971ebb1ba} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:70: Get all tenant info from cache
2023-12-12 09:28:19.516 [DEBU] {104c9b8a4df19f1710cdce69e60c1164} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-12-12 09:29:26.636 [DEBU] {08744c185df19f1711cdce6967a2379e} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-12-12 09:33:53.604 [DEBU] {e8f3da4a9bf19f1712cdce69eccd115c} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-12-12 09:47:14.898 [DEBU] {a0e810da55f29f1713cdce695c3d336b} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-12-12 09:50:56.254 [DEBU] {d029406589f29f1714cdce69a7310a33} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-12-12 10:03:50.890 [DEBU] {285cb4c03df39f1715cdce69c6ebf20f} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-12-12 10:05:50.209 [DEBU] {003aed8859f39f1716cdce69cdb37faf} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-12-12 10:07:02.943 [DEBU] {189db7786af39f1717cdce69c35e4066} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-12-12 10:10:43.288 [DEBU] {a83711c69df39f1718cdce69b2ae419a} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-12-12 10:44:34.229 [DEBU] {00e302ab76f59f1719cdce6942bca85c} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-12-12 10:46:38.204 [DEBU] {b0cbb58893f59f171acdce6982227f26} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-12-12 10:48:06.932 [DEBU] {90e0ed32a8f59f171bcdce6979cc3b85} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-12-12 10:51:06.830 [DEBU] {20597e15d2f59f171ccdce6915d8559c} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
