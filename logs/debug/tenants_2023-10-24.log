2023-10-24 16:28:58.554 [DEBU] {68e38800d0fd901744ff2314d0640121} Get all tenant info from cache
2023-10-24 16:28:58.561 [DEBU] {68e38800d0fd901744ff2314d0640121} &{TenantID:Tenant04 Password:1234abcd APPKey:61db299a-2056-416a-bfc4-59cc4d3e67b2 Available:true}
2023-10-24 16:30:32.966 [DEBU] {e810460ae8fd90171ab2a0355bd67fcd} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:70: Get all tenant info from cache
2023-10-24 16:35:39.778 [DEBU] {b05ae3902ffe90171bb2a0355125ff67} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-10-24 16:35:39.831 [DEBU] {b05ae3902ffe90171bb2a0355125ff67} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-10-24 16:36:17.942 [DEBU] {e8ad077438fe90171cb2a0359b6168ad} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-10-24 16:37:40.305 [DEBU] {10f61aa14bfe90171db2a0352d9cbe1d} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:70: Get all tenant info from cache
2023-10-24 16:45:54.151 [DEBU] {a0ac4576befe901701865177234996e3} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-10-24 16:50:03.490 [DEBU] {80dd449ff8fe9017ac01552100ff5e75} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:70: Get all tenant info from cache
2023-10-24 17:02:39.695 [DEBU] {68e38800d0fd901744ff2314d0640121} Get all tenant info from cache
2023-10-24 17:02:39.743 [DEBU] {68e38800d0fd901744ff2314d0640121} &{TenantID:Tenant04 Password:1234abcd APPKey:61db299a-2056-416a-bfc4-59cc4d3e67b2 Available:true}
2023-10-24 17:05:36.126 [DEBU] {58906aced1ff9017ad015521ff93c5c9} [tenants/internal/service.(*Tenant).RefreshToken] tenant_service.go:834: Refresh token, tenantID :Tenant04
2023-10-24 17:05:36.126 [DEBU] {58906aced1ff9017ad015521ff93c5c9} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-10-24 17:07:31.171 [DEBU] {f0f23f39ecff9017ae0155217c8a2618} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:30: &{TenantID:Tenant01 Password:1234abcd APPKey: Available:true}
2023-10-24 17:07:36.736 [DEBU] {c884c9e4edff9017af0155217d19f957} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:70: Get all tenant info from cache
2023-10-24 17:08:38.541 [DEBU] {70f08f47fcff9017b001552180f2bb83} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant01
2023-10-24 17:08:39.264 [DEBU] {70f08f47fcff9017b001552180f2bb83} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:30: &{TenantID:Tenant01 Password:1234abcd APPKey:90332165-669f-4b30-836d-17dce5d98798 Available:true}
2023-10-24 17:12:48.475 [DEBU] {605f6a7936009117b1015521b745912e} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant01
2023-10-24 17:13:33.016 [DEBU] {18cf52d740009117b3015521fa65c3ad} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:70: Get all tenant info from cache
2023-10-24 17:13:42.895 [DEBU] {68d07a2443009117b4015521054e8bf0} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant01
2023-10-24 17:13:44.441 [DEBU] {68d07a2443009117b4015521054e8bf0} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:30: &{TenantID:Tenant01 Password:abcd1234 APPKey:90332165-669f-4b30-836d-17dce5d98798 Available:true}
2023-10-24 17:13:48.408 [DEBU] {8076cc6d44009117b501552133bfd2d9} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:70: Get all tenant info from cache
2023-10-24 17:14:22.211 [DEBU] {0027664b4c009117b6015521211ae58d} [tenants/internal/service.(*Tenant).RefreshToken] tenant_service.go:834: Refresh token, tenantID :Tenant01
2023-10-24 17:14:22.211 [DEBU] {0027664b4c009117b6015521211ae58d} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant01
2023-10-24 17:14:22.232 [DEBU] {0027664b4c009117b6015521211ae58d} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant01
2023-10-24 17:21:28.303 [DEBU] {d090c795a70091179670e87f437ca46a} Get all tenant info from cache
2023-10-24 17:21:36.765 [DEBU] {887b3c3db10091174ccca20dad6c6afa} Get all tenant info from cache
2023-10-24 17:21:36.768 [DEBU] {887b3c3db10091174ccca20dad6c6afa} &{TenantID:Tenant04 Password:1234abcd APPKey:61db299a-2056-416a-bfc4-59cc4d3e67b2 Available:true}
2023-10-24 17:21:36.768 [DEBU] {887b3c3db10091174ccca20dad6c6afa} &{TenantID:Tenant01 Password:abcd1234 APPKey:90332165-669f-4b30-836d-17dce5d98798 Available:true}
2023-10-24 17:21:41.671 [DEBU] {a8215f9eb2009117b801552192d49c33} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:70: Get all tenant info from cache
2023-10-24 17:22:40.830 [DEBU] {8061fd61c0009117b9015521ad20bf36} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant01
2023-10-24 17:24:11.440 [DEBU] {800d5d7dd5009117ba015521f0f130b0} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:70: Get all tenant info from cache
2023-10-24 17:24:31.441 [DEBU] {38203224da009117bb0155219b19acdb} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant01
2023-10-24 17:24:32.885 [DEBU] {38203224da009117bb0155219b19acdb} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:30: &{TenantID:Tenant01 Password:1234abcd APPKey:90332165-669f-4b30-836d-17dce5d98798 Available:true}
2023-10-24 17:24:37.859 [DEBU] {d058a3a4db009117bc0155215b462726} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:70: Get all tenant info from cache
2023-10-24 17:24:55.629 [DEBU] {a0b1e9c7df009117bd015521de5f5eb5} [tenants/internal/service.(*Tenant).RefreshToken] tenant_service.go:834: Refresh token, tenantID :Tenant01
2023-10-24 17:24:55.629 [DEBU] {a0b1e9c7df009117bd015521de5f5eb5} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant01
2023-10-24 17:25:24.865 [DEBU] {106fbe7de6009117be015521ec3bae64} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:30: &{TenantID:Tenant02 Password:1234abcd APPKey: Available:true}
2023-10-24 17:25:31.105 [DEBU] {9005d909e8009117bf015521f5225402} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:70: Get all tenant info from cache
2023-10-24 17:25:39.308 [DEBU] {d8e20ff2e9009117c0015521af56e211} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant02
2023-10-24 17:25:39.634 [DEBU] {d8e20ff2e9009117c0015521af56e211} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:30: &{TenantID:Tenant02 Password:1234abcd APPKey:9c1a68a2-82a5-4507-90a9-9aea78aa40e4 Available:true}
2023-10-24 17:26:10.018 [DEBU] {a0c1a819f1009117c101552174bcce3e} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant01
2023-10-24 17:26:10.032 [DEBU] {a0c1a819f1009117c101552174bcce3e} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant01
2023-10-24 17:26:51.215 [DEBU] {58a51cb0fa009117c201552146b743f5} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant02
2023-10-24 17:27:22.704 [DEBU] {20c7fe0402019117c301552192ce16cc} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant02
2023-10-24 17:27:24.334 [DEBU] {20c7fe0402019117c301552192ce16cc} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:30: &{TenantID:Tenant02 Password:abcd1234 APPKey:9c1a68a2-82a5-4507-90a9-9aea78aa40e4 Available:true}
2023-10-24 17:27:42.073 [DEBU] {20e1938806019117c4015521f4844e03} [tenants/internal/service.(*Tenant).RefreshToken] tenant_service.go:834: Refresh token, tenantID :Tenant02
2023-10-24 17:27:42.074 [DEBU] {20e1938806019117c4015521f4844e03} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant02
2023-10-24 17:27:42.084 [DEBU] {20e1938806019117c4015521f4844e03} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant02
2023-10-24 17:32:31.443 [DEBU] {78cc2ae849019117c5015521a439c294} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:70: Get all tenant info from cache
2023-10-24 17:32:54.893 [DEBU] {481ee85d4f019117c60155219fe54c32} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant02
2023-10-24 17:33:09.739 [DEBU] {20346ad152019117c7015521348becfc} [tenants/internal/service.(*Tenant).RefreshToken] tenant_service.go:834: Refresh token, tenantID :Tenant02
2023-10-24 17:33:09.739 [DEBU] {20346ad152019117c7015521348becfc} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant02
