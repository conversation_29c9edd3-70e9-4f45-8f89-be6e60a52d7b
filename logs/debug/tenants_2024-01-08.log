2024-01-08 13:35:51.934 [DEBU] {00d2b5b57848a817e873be302a085ab7} Get all tenant info from cache
2024-01-08 13:35:51.953 [DEBU] {00d2b5b57848a817e873be302a085ab7} &{TenantID:R20230718-0001 Password:1qaz@WSX3edc APPKey:854a3e1a-234a-491d-bb3f-b12ad1a7224a Available:true}
2024-01-08 13:35:51.954 [DEBU] {00d2b5b57848a817e873be302a085ab7} &{TenantID:Tenant04 Password:1qaz@WSX3edc APPKey:36a8bc40-08b9-4944-a6f5-0f4b9037ebbd Available:true}
2024-01-08 13:35:51.954 [DEBU] {00d2b5b57848a817e873be302a085ab7} &{TenantID:Z20231222-0001 Password:!QAZ2wsx APPKey:9eea7e9d-5d9d-45b4-bdca-94c5123062bb Available:true}
2024-01-08 13:35:51.954 [DEBU] {00d2b5b57848a817e873be302a085ab7} &{TenantID:R20240105-0001 Password:1qaz@WSX APPKey:38e4a5cb-f0d4-4bd7-96e3-c1fdf3d5bb2b Available:true}
2024-01-08 13:35:51.954 [DEBU] {00d2b5b57848a817e873be302a085ab7} &{TenantID:GPT-20230704-0002 Password:1qaz@WSX3edc APPKey:4bf262c1-0f66-4641-a0fa-f2da216a1954 Available:true}
2024-01-08 13:35:51.954 [DEBU] {00d2b5b57848a817e873be302a085ab7} &{TenantID:R20230704-0001 Password:QAzxws12! APPKey:df0153d6-dcf0-42fe-a8f0-038b5592ead3 Available:true}
2024-01-08 13:35:51.954 [DEBU] {00d2b5b57848a817e873be302a085ab7} &{TenantID:GPT-20230704-0003 Password:@3eQfdfffgh,_ APPKey:32ccd6ef-3aa2-49a7-820f-5df452811e37 Available:true}
2024-01-08 13:35:51.954 [DEBU] {00d2b5b57848a817e873be302a085ab7} &{TenantID:R20240103-001 Password:!QAZ2wsx APPKey: Available:true}
2024-01-08 13:35:51.955 [DEBU] {00d2b5b57848a817e873be302a085ab7} &{TenantID:GPT-20230704-0001 Password:!QAZ2wsx APPKey:95e71b4f-9080-4e56-a390-d22495a47d96 Available:true}
2024-01-08 13:35:51.955 [DEBU] {00d2b5b57848a817e873be302a085ab7} &{TenantID:GPT-20230704-0004 Password:Q123@|_-%/<>w APPKey:b10bcdb4-acb8-431a-a5ec-c5e64d4614bd Available:true}
2024-01-08 13:36:43.862 [DEBU] {507d35098748a817a94d605453195ac1} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID GPT-20230704-0002
2024-01-08 13:36:47.872 [DEBU] {801575fb8748a817aa4d6054242589fb} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID GPT-20230704-0002
2024-01-08 13:37:28.175 [DEBU] {30f75b4c9148a817ab4d60540fd027b1} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID GPT-20230704-0002
