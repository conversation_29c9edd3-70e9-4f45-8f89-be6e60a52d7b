2023-11-02 09:48:05.585 [DEBU] {d8dbadc22aab9317cd6ce531f4e30ec6} Get all tenant info from cache
2023-11-02 09:48:05.593 [DEBU] {d8dbadc22aab9317cd6ce531f4e30ec6} &{TenantID:Tenant04 Password:1234abcd APPKey:61db299a-2056-416a-bfc4-59cc4d3e67b2 Available:true}
2023-11-02 09:48:05.593 [DEBU] {d8dbadc22aab9317cd6ce531f4e30ec6} &{TenantID:Tenant01 Password:1234abcd APPKey:90332165-669f-4b30-836d-17dce5d98798 Available:true}
2023-11-02 09:48:05.593 [DEBU] {d8dbadc22aab9317cd6ce531f4e30ec6} &{TenantID:Tenant02 Password:abcd1234 APPKey:9c1a68a2-82a5-4507-90a9-9aea78aa40e4 Available:false}
2023-11-02 09:49:43.249 [DEBU] {58818eb441ab93170b55047c7fb8dead} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 09:49:46.658 [DEBU] {589e178642ab93170c55047c37ca7859} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 09:49:48.199 [DEBU] {f0e3f6e142ab93170d55047c3e102645} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 09:49:51.693 [DEBU] {003c59b243ab93170e55047c0db521b7} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 09:49:55.100 [DEBU] {30826c7d44ab93170f55047c605333cd} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 09:49:55.129 [DEBU] {507f2f7f44ab93171055047cdfbef296} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 09:49:55.148 [DEBU] {c8fe4f8044ab93171155047cdeb4c746} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 10:42:01.270 [DEBU] {38c4150f1cae9317e687764536244936} Get all tenant info from cache
2023-11-02 10:42:01.275 [DEBU] {38c4150f1cae9317e687764536244936} &{TenantID:Tenant04 Password:1234abcd APPKey:61db299a-2056-416a-bfc4-59cc4d3e67b2 Available:true}
2023-11-02 10:42:01.276 [DEBU] {38c4150f1cae9317e687764536244936} &{TenantID:Tenant01 Password:1234abcd APPKey:90332165-669f-4b30-836d-17dce5d98798 Available:true}
2023-11-02 10:42:01.276 [DEBU] {38c4150f1cae9317e687764536244936} &{TenantID:Tenant02 Password:abcd1234 APPKey:9c1a68a2-82a5-4507-90a9-9aea78aa40e4 Available:false}
2023-11-02 12:03:10.615 [DEBU] {7822dc148ab293171255047ce0e2faec} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 12:04:38.049 [DEBU] {98c3c7719eb293171355047cbd0081c6} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 13:21:30.108 [DEBU] {68405d45d0b693171455047c16266717} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 13:21:32.417 [DEBU] {5881cecfd0b693171555047cd122f35c} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 14:25:49.063 [DEBU] {805f30bf52ba93171655047ce2cb13f3} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 14:42:59.635 [DEBU] {d075eeb342bb93171755047c503dd31c} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 14:52:50.008 [DEBU] {a0973429ccbb93171855047cadf75ff0} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 15:10:03.346 [DEBU] {f0f5f6c1bcbc93171955047cca9efbfc} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 15:44:04.717 [DEBU] {c0e7270b98be93171a55047c903ea5c3} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 15:44:10.753 [DEBU] {5072de7499be93171b55047ce6c0b472} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 15:44:11.221 [DEBU] {5833e09099be93171c55047c04aae032} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 15:44:23.536 [DEBU] {58b4ec6e9cbe93171d55047c06f4ccef} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 15:58:08.593 [DEBU] {30c7ab865cbf93171e55047ca1b1e83e} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:01:26.750 [DEBU] {804bfea98abf93172055047c0beb09d7} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:01:29.317 [DEBU] {006d3a448bbf93172155047c71587d84} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:01:48.192 [DEBU] {e83359a98fbf93172255047c5844fce4} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:02:14.747 [DEBU] {60bcc4d795bf93172455047c29f2ce15} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:02:24.686 [DEBU] {20bb852898bf93172555047c70c27fd0} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:03:12.859 [DEBU] {b85ecb5fa3bf93172655047c2eab3c16} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:03:12.941 [DEBU] {3038c564a3bf93172755047c41b53661} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:05:16.104 [DEBU] {68d26011c0bf93172955047cf4e84f5c} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:06:11.986 [DEBU] {886f4d14cdbf93172b55047c0facd639} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:06:23.061 [DEBU] {70fb88a8cfbf93172c55047c078b9f26} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:07:28.793 [DEBU] {582a6bf5debf93172e55047c2950f709} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:08:52.933 [DEBU] {384b378df2bf93173055047ce1ff40a7} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:09:08.003 [DEBU] {70a88f0ff6bf93173155047c77a7903f} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:09:17.629 [DEBU] {f89eb74df8bf93173255047cc925cb95} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:09:38.101 [DEBU] {7043fa11fdbf93173355047c8d6ec47a} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:09:44.378 [DEBU] {60d83488febf93173455047c778de49a} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:10:04.781 [DEBU] {6897e04703c093173555047c4537fb39} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:10:17.353 [DEBU] {f8239c3506c093173655047c496566ed} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:10:21.240 [DEBU] {70ae471d07c093173755047cb84e3ef1} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:10:33.671 [DEBU] {407d48020ac093173855047c4206e811} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:10:41.591 [DEBU] {f01963da0bc093173955047ca56274ce} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:10:43.449 [DEBU] {2815df480cc093173a55047c72a1d4bb} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:15:59.801 [DEBU] {a8808ef055c093173b55047c345020fd} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:18:15.889 [DEBU] {38da61a075c093173c55047c89bf26b5} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:18:24.656 [DEBU] {a80528ab77c093173d55047cf498cee8} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:18:34.558 [DEBU] {d85859f979c093173e55047c7a366d14} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:18:44.988 [DEBU] {c88f08677cc093173f55047ce5066a16} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:18:45.028 [DEBU] {10188b697cc093174055047cf5e2270b} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:18:48.808 [DEBU] {d0fdb74a7dc093174255047c7423ce74} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:19:46.665 [DEBU] {00f8cbc28ac093174455047c89c735b4} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:20:20.364 [DEBU] {2061d09b92c093174655047cd2df47e7} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:20:20.427 [DEBU] {2842b69f92c093174755047c9291a4ed} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:20:32.999 [DEBU] {408f708c95c093174855047cfb39e4ce} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:24:28.877 [DEBU] {c8d8f977ccc093174a55047c90bcf6c5} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:25:10.779 [DEBU] {6819e939d6c093174c55047c7069791f} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-02 16:25:13.439 [DEBU] {20ed4fd8d6c093174d55047cb8b02919} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
