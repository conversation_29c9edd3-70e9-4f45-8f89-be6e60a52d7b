2023-10-23 17:04:25.308 [DEBU] {8828f4c02cb190173622034bcab8821c} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:04:25.331 [DEBU] {8828f4c02cb190173622034bcab8821c} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:27: &{TenantID:Tenant04 Password:1234abcd APPKey:61db299a-2056-416a-bfc4-59cc4d3e67b2 Available:true}
2023-10-23 17:04:51.460 [DEBU] {28cb2fd832b190173722034b2d1484b8} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:05:36.785 [DEBU] {0853a8063db190173822034be1e29334} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:27: &{TenantID:Tenant01 Password:1234abcd APPKey: Available:true}
2023-10-23 17:05:42.462 [DEBU] {509533b83eb190173922034b56dd3928} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:06:38.069 [DEBU] {70c620a94bb190173a22034b90d65dc5} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:45: tenantID Tenant01
2023-10-23 17:06:38.404 [DEBU] {70c620a94bb190173a22034b90d65dc5} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:27: &{TenantID:Tenant01 Password:1234abcd APPKey:cd1d24ea-341f-460b-a78b-b0e558bb9a82 Available:true}
2023-10-23 17:07:00.285 [DEBU] {18ada5d650b190173b22034b0a6403ec} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:07:44.217 [DEBU] {a888ec0f5bb190173c22034b50fe303b} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:45: tenantID Tenant01
2023-10-23 17:07:44.220 [DEBU] {a888ec0f5bb190173c22034b50fe303b} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:27: &{TenantID:Tenant01 Password:abcd1234 APPKey:cd1d24ea-341f-460b-a78b-b0e558bb9a82 Available:true}
2023-10-23 17:10:04.374 [DEBU] {408c0bb27bb190173d22034bea920621} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:45: tenantID Tenant01
2023-10-23 17:10:04.374 [DEBU] {408c0bb27bb190173d22034bea920621} [tenants/internal/service.(*Tenant).validateTenantPass] tenant_service.go:150: tenantID :  Tenant01 not in cache
2023-10-23 17:10:04.377 [DEBU] {408c0bb27bb190173d22034bea920621} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:27: &{TenantID:Tenant01 Password:1234abcd APPKey:cd1d24ea-341f-460b-a78b-b0e558bb9a82 Available:true}
2023-10-23 17:10:05.840 [DEBU] {408c0bb27bb190173d22034bea920621} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:27: &{TenantID:Tenant01 Password:abcd1234 APPKey:cd1d24ea-341f-460b-a78b-b0e558bb9a82 Available:true}
2023-10-23 17:10:37.936 [DEBU] {8881ea8383b190173e22034b900ec2d2} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:13:01.738 [DEBU] {c0afe7ebaea190172b29aa6d97033b21} Get all tenant info from cache
2023-10-23 17:13:01.745 [DEBU] {c0afe7ebaea190172b29aa6d97033b21} &{TenantID:Tenant04 Password:1234abcd APPKey:61db299a-2056-416a-bfc4-59cc4d3e67b2 Available:true}
2023-10-23 17:13:01.745 [DEBU] {c0afe7ebaea190172b29aa6d97033b21} &{TenantID:Tenant01 Password:abcd1234 APPKey:cd1d24ea-341f-460b-a78b-b0e558bb9a82 Available:true}
2023-10-23 17:13:06.987 [DEBU] {38541838a6b190173f22034b885799ad} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:13:25.113 [DEBU] {8896096faab190174022034b8309b24c} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:45: tenantID Tenant01
2023-10-23 17:13:46.573 [DEBU] {7844f46dafb190174122034b0b262b20} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:45: tenantID Tenant01
2023-10-23 17:13:48.025 [DEBU] {7844f46dafb190174122034b0b262b20} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:27: &{TenantID:Tenant01 Password:1234abcd APPKey:cd1d24ea-341f-460b-a78b-b0e558bb9a82 Available:true}
2023-10-23 17:13:56.050 [DEBU] {f00eb5a4b1b190174222034b75dd6732} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:14:23.806 [DEBU] {180e3efdb7b190174322034bf9928978} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:27: &{TenantID:Tenant01 Password:1234abcd APPKey:cd1d24ea-341f-460b-a78b-b0e558bb9a82 Available:false}
2023-10-23 17:14:23.807 [DEBU] {180e3efdb7b190174322034bf9928978} [tenants/internal/service.(*Tenant).removeFromCache] tenant_service.go:32: tenantID Tenant01
2023-10-23 17:14:28.627 [DEBU] {505b613ab9b190174422034b78d82b5a} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:15:11.826 [DEBU] {c81e1a46c3b190174522034bc420fd42} [tenants/internal/service.(*Tenant).removeFromCache] tenant_service.go:32: tenantID Tenant01
2023-10-23 17:15:24.511 [DEBU] {3802463cc6b190174622034bfd77f072} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:17:01.273 [DEBU] {401d75c4dcb190174722034b4d6f977a} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:17:10.218 [DEBU] {608489f5ddb190174822034b49f9cdb7} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:27: &{TenantID:Tenant01 Password:1234abcd APPKey: Available:true}
2023-10-23 17:17:16.901 [DEBU] {884d0f68e0b190174922034ba5b98abd} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:17:33.591 [DEBU] {788d0d4ae4b190174a22034b687e30a1} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:45: tenantID Tenant01
2023-10-23 17:17:33.929 [DEBU] {788d0d4ae4b190174a22034b687e30a1} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:27: &{TenantID:Tenant01 Password:1234abcd APPKey:39376e76-40e9-4f2e-bc24-269d00a13758 Available:true}
2023-10-23 17:17:38.396 [DEBU] {28f07569e5b190174b22034ba333780a} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:17:46.408 [DEBU] {00723544e7b190174c22034be3efa214} [tenants/internal/service.(*Tenant).removeFromCache] tenant_service.go:32: tenantID Tenant01
2023-10-23 17:17:54.213 [DEBU] {50a33d18e9b190174d22034b271a180f} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:18:04.659 [DEBU] {7856bc70ebb190174e22034be753afa6} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:27: &{TenantID:Tenant01 Password:1234abcd APPKey: Available:true}
2023-10-23 17:18:07.990 [DEBU] {10f0754decb190174f22034b14b923fb} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:18:20.100 [DEBU] {e87eae0aefb190175022034b79e95413} [tenants/internal/service.(*Tenant).setTenantInfoInCache] tenant_service.go:27: &{TenantID:Tenant01 Password:1234abcd APPKey: Available:false}
2023-10-23 17:18:20.100 [DEBU] {e87eae0aefb190175022034b79e95413} [tenants/internal/service.(*Tenant).removeFromCache] tenant_service.go:32: tenantID Tenant01
2023-10-23 17:18:23.554 [DEBU] {a89c1fedefb190175122034b8e82aa17} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:18:34.958 [DEBU] {4032a104f2b190173e6f731473a46003} Get all tenant info from cache
2023-10-23 17:18:34.962 [DEBU] {4032a104f2b190173e6f731473a46003} &{TenantID:Tenant04 Password:1234abcd APPKey:61db299a-2056-416a-bfc4-59cc4d3e67b2 Available:true}
2023-10-23 17:18:34.962 [DEBU] {4032a104f2b190173e6f731473a46003} &{TenantID:Tenant01 Password:1234abcd APPKey: Available:false}
2023-10-23 17:18:38.963 [DEBU] {306be482f3b190175222034bca09d444} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:20:14.698 [DEBU] {4032a104f2b190173e6f731473a46003} Get all tenant info from cache
2023-10-23 17:20:14.703 [DEBU] {4032a104f2b190173e6f731473a46003} &{TenantID:Tenant04 Password:1234abcd APPKey:61db299a-2056-416a-bfc4-59cc4d3e67b2 Available:true}
2023-10-23 17:20:14.703 [DEBU] {4032a104f2b190173e6f731473a46003} &{TenantID:Tenant01 Password:1234abcd APPKey: Available:false}
2023-10-23 17:20:22.240 [DEBU] {201b168f0bb290175322034bfa5e2647} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:20:43.563 [DEBU] {7876b78510b290175522034b970ed342} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:21:08.564 [DEBU] {20f2cb5616b290175622034ba5b18ef5} [tenants/internal/service.(*Tenant).removeFromCache] tenant_service.go:32: tenantID Tenant01
2023-10-23 17:21:12.108 [DEBU] {0842cd2b17b290175722034b78a61419} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:21:28.408 [DEBU] {187779731ab290176484c2231adcbff8} Get all tenant info from cache
2023-10-23 17:21:28.412 [DEBU] {187779731ab290176484c2231adcbff8} &{TenantID:Tenant04 Password:1234abcd APPKey:61db299a-2056-416a-bfc4-59cc4d3e67b2 Available:true}
2023-10-23 17:21:31.694 [DEBU] {9882e6ba1bb290175822034bdd92a39a} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:65: Get all tenant info from cache
2023-10-23 17:25:31.914 [DEBU] {187779731ab290176484c2231adcbff8} Get all tenant info from cache
2023-10-23 17:25:31.926 [DEBU] {187779731ab290176484c2231adcbff8} &{TenantID:Tenant04 Password:1234abcd APPKey:61db299a-2056-416a-bfc4-59cc4d3e67b2 Available:true}
2023-10-23 17:25:50.923 [DEBU] {f818a41558b290175922034b3e9afd71} [tenants/internal/service.(*Tenant).getAllTenantsInfoFromCache] tenant_service.go:67: Get all tenant info from cache
