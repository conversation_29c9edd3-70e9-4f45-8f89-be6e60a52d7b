2024-01-09 09:56:52.425 [DEBU] {386fc4371b8ba817843c3c034cc2c476} Get all tenant info from cache
2024-01-09 09:56:52.447 [DEBU] {386fc4371b8ba817843c3c034cc2c476} &{TenantID:R20230718-0001 Password:1qaz@WSX3edc APPKey:854a3e1a-234a-491d-bb3f-b12ad1a7224a Available:true}
2024-01-09 09:56:52.447 [DEBU] {386fc4371b8ba817843c3c034cc2c476} &{TenantID:Tenant04 Password:1qaz@WSX3edc APPKey:36a8bc40-08b9-4944-a6f5-0f4b9037ebbd Available:true}
2024-01-09 09:56:52.448 [DEBU] {386fc4371b8ba817843c3c034cc2c476} &{TenantID:Z20231222-0001 Password:!QAZ2wsx APPKey:9eea7e9d-5d9d-45b4-bdca-94c5123062bb Available:true}
2024-01-09 09:56:52.448 [DEBU] {386fc4371b8ba817843c3c034cc2c476} &{TenantID:R20240105-0001 Password:1qaz@WSX@12qW1 APPKey:16eccee5-c533-4759-ae40-7d722ab6e18a Available:true}
2024-01-09 09:56:52.448 [DEBU] {386fc4371b8ba817843c3c034cc2c476} &{TenantID:GPT-20230704-0002 Password:1qaz@WSX3edc APPKey:4bf262c1-0f66-4641-a0fa-f2da216a1954 Available:true}
2024-01-09 09:56:52.448 [DEBU] {386fc4371b8ba817843c3c034cc2c476} &{TenantID:R20230704-0001 Password:QAzxws12! APPKey:62b70ed0-54a9-4816-b013-b76bdbb9f873 Available:true}
2024-01-09 09:56:52.448 [DEBU] {386fc4371b8ba817843c3c034cc2c476} &{TenantID:GPT-20230704-0003 Password:@3eQfdfffgh,_ APPKey:2576524b-5581-4d08-a709-061a5264afa5 Available:true}
2024-01-09 09:56:52.448 [DEBU] {386fc4371b8ba817843c3c034cc2c476} &{TenantID:R20240103-001 Password:!QAZ2wsx APPKey: Available:true}
2024-01-09 09:56:52.448 [DEBU] {386fc4371b8ba817843c3c034cc2c476} &{TenantID:R20240108-0001 Password:Qaw@345g* APPKey:4b6510fb-d58a-4d02-9f1a-7e69bafa94a9 Available:true}
2024-01-09 09:56:52.448 [DEBU] {386fc4371b8ba817843c3c034cc2c476} &{TenantID:GPT-20230704-0001 Password:!QAZ2wsx APPKey:95e71b4f-9080-4e56-a390-d22495a47d96 Available:true}
2024-01-09 09:56:52.448 [DEBU] {386fc4371b8ba817843c3c034cc2c476} &{TenantID:GPT-20230704-0004 Password:Q123@|_-%/<>w APPKey:b10bcdb4-acb8-431a-a5ec-c5e64d4614bd Available:true}
2024-01-09 09:57:22.868 [DEBU] {b8b7a751238ba81707b8376140057622} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID GPT-20230704-0002
2024-01-09 10:02:32.641 [DEBU] {9881e6756b8ba81708b837610579da1e} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID GPT-20230704-0002
2024-01-09 10:03:25.689 [DEBU] {f0e6c6d0778ba81709b8376183b10811} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID GPT-20230704-0002
2024-01-09 10:16:41.508 [DEBU] {784aa51a318ca8170ab8376140824979} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID GPT-20230704-0002
