2023-11-15 11:08:20.111 [DEBU] {8034050f17ad97175e26bf40cab517ad} Get all tenant info from cache
2023-11-15 11:08:20.113 [DEBU] {8034050f17ad97175e26bf40cab517ad} &{TenantID:Tenant04 Password:1234abcd APPKey:d06a3c67-a88a-4712-8d5b-a572e9a68392 Available:true}
2023-11-15 11:08:46.580 [DEBU] {6800437b1dad971739bb110c5a7975cf} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:08:50.159 [DEBU] {70d1d9551ead97173abb110ccd2dbe3a} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:08:53.651 [DEBU] {988271251fad97173bbb110cfbaa8647} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:08:56.020 [DEBU] {888842b31fad97173cbb110c76c15606} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:09:00.600 [DEBU] {28e628c420ad97173dbb110cdf3f2ba4} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:09:00.623 [DEBU] {48b722c520ad97173ebb110c932921c6} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:09:00.683 [DEBU] {482038c920ad97173fbb110c35057fcc} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:09:56.105 [DEBU] {70d782b02dad971740bb110c13a3aaa8} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:09:56.172 [DEBU] {48119ab42dad971741bb110c2b810365} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:09:56.204 [DEBU] {48ff90b62dad971742bb110c7812c6e4} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:09:57.125 [DEBU] {70a202ed2dad971743bb110c9484d666} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:09:57.149 [DEBU] {40c9dcee2dad971744bb110cda8d51ea} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:09:57.176 [DEBU] {f0c272f02dad971745bb110c402aa0e5} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:09:59.292 [DEBU] {809add6d2ead971746bb110c87a488e3} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:09:59.325 [DEBU] {50b789702ead971747bb110c1df476bd} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:10:01.183 [DEBU] {d0c653df2ead971748bb110c31ff4e00} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:10:01.262 [DEBU] {50cc00e42ead971749bb110cc0d69b3c} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:10:04.064 [DEBU] {80e2f98a2fad97174abb110c90ddb2fa} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:10:05.654 [DEBU] {a06caae92fad97174bbb110cda6435ce} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:10:07.439 [DEBU] {385f285430ad97174cbb110c8d8ddf9f} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:10:08.992 [DEBU] {9029bdb030ad97174dbb110c6919fd31} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:10:27.115 [DEBU] {705d68e834ad97174ebb110cb0876650} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:10:27.151 [DEBU] {c08907eb34ad97174fbb110cbd72360e} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:11:35.659 [DEBU] {d0f451de44ad971750bb110c957cd9b0} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:11:35.696 [DEBU] {c033b2e044ad971751bb110c52f7cb93} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:17:28.343 [DEBU] {28a723fb96ad971752bb110c16480e26} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:33:46.301 [DEBU] {38a343ae7aae971753bb110c1fc0db8f} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:33:46.410 [DEBU] {a85853b57aae971754bb110ce3613a4b} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:33:49.920 [DEBU] {c06b60867bae971755bb110c9dd5f0f9} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:33:49.944 [DEBU] {20c504887bae971756bb110c449fa069} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:33:50.002 [DEBU] {80dd928b7bae971757bb110c64b032a0} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:38:24.428 [DEBU] {b87c5f70bbae971758bb110c5c5e67b9} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:38:24.520 [DEBU] {80991576bbae971759bb110cbbba1fb1} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 11:38:24.538 [DEBU] {d0013477bbae97175abb110cc6a0e9ad} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:05:45.625 [DEBU] {68173a8f39b097175bbb110c5f91601d} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:05:45.685 [DEBU] {3026289339b097175cbb110c74d109d0} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:07:28.690 [DEBU] {a891a48e51b097175dbb110cce763348} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:07:28.778 [DEBU] {c005f09351b097175ebb110cea86687b} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:10:44.273 [DEBU] {28a5fa177fb097175fbb110ca92e0627} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:18:34.531 [DEBU] {902f3b95ecb0971760bb110c39ae43ef} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:18:46.824 [DEBU] {a8a47872efb0971761bb110cf6c20e14} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:18:47.924 [DEBU] {e0c302b4efb0971762bb110cab7c1f46} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:18:53.306 [DEBU] {b0630af5f0b0971763bb110c0a290286} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:18:54.805 [DEBU] {5854634ef1b0971764bb110ce89245b5} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:18:56.156 [DEBU] {5891d59ef1b0971765bb110cc02e14cf} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:18:57.388 [DEBU] {80b5fee7f1b0971766bb110c28dc23c5} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:19:18.585 [DEBU] {b8f3acd7f6b0971767bb110c37c4747e} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:22:30.442 [DEBU] {40dead8223b1971768bb110c825ecdd4} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:22:33.029 [DEBU] {10f70b1d24b1971769bb110ce4cfd365} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:22:34.785 [DEBU] {18592e8624b197176abb110ce837dccc} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:22:36.573 [DEBU] {083f89f024b197176bbb110c5eb735de} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:22:40.475 [DEBU] {904cf6d825b197176cbb110c5e15f522} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:22:50.588 [DEBU] {c8ae7c3328b197176dbb110c12ad8c0f} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:22:50.612 [DEBU] {b814983528b197176ebb110c02f2a127} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:23:09.759 [DEBU] {b009beaa2cb197176fbb110c09165a1d} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:23:12.859 [DEBU] {10a572632db1971770bb110c209ead8e} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:23:13.637 [DEBU] {b89ae7912db1971771bb110cfd3e0e32} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:23:20.347 [DEBU] {d8df91212fb1971772bb110c99b57e21} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:23:22.986 [DEBU] {00e039bf2fb1971773bb110cec787828} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
2023-11-15 12:23:55.406 [DEBU] {d817374b37b1971774bb110ceb0e88e2} [tenants/internal/service.(*Tenant).getTenantInfoByIDFromCache] tenant_service.go:50: tenantID Tenant04
