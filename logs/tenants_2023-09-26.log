2023-09-26 09:32:27.779 [INFO] {5871fdebd64e8817ed40686bfdaf3d98} [tenants/internal/service.(*Tenant).DisableTenant] tenant_service.go:349: {
	"data": {
		"Get": {
			"Tenants": [
				{
					"_additional": {
						"id": "589928aa-3775-4d1e-8688-de2241128b57"
					},
					"tenant_Id": "Tenant03"
				}
			]
		}
	}
}
2023-09-26 09:35:35.537 [INFO] {c818beba024f8817ee40686b6fceec35} [tenants/internal/service.(*Tenant).DisableTenant] tenant_service.go:354: {
	"data": {
		"Get": {
			"Tenants": [
				{
					"_additional": {
						"id": "589928aa-3775-4d1e-8688-de2241128b57"
					},
					"app_Key": "f6f1766d-1d31-4961-b8bf-825a199485f3",
					"available": true,
					"password": "abcd1234",
					"tenant_Id": "Tenant03"
				}
			]
		}
	}
}
2023-09-26 09:40:54.607 [INFO] {e0776a2c404f8817f040686b083a188f} [tenants/internal/service.(*Tenant).DisableTenant] tenant_service.go:355: map[_additional:map[id:589928aa-3775-4d1e-8688-de2241128b57] app_Key:f6f1766d-1d31-4961-b8bf-825a199485f3 available:true password:abcd1234 tenant_Id:Tenant03]
