2025-06-12T18:16:52.830+08:00 [DEBU] {f0408e3bf03b4818bb1e334ccbf4bb6c} [tenants/internal/logic/messageQ.(*sMessageQ).Send] messageQ.go:63: Send: route key "mariadb.tenant", action "createSchema" , data: {
	"data": null,
	"schema": "dsh",
	"table": "",
	"where_conditions": "",
	"where_params": null
}
2025-06-12T18:16:52.831+08:00 [DEBU] {f0408e3bf03b4818bb1e334ccbf4bb6c} [tenants/internal/logic/messageQ.(*sMessageQ).Send] messageQ.go:63: Send: route key "mariadb.tenant", action "createTable" , data: {
	"data": {
		"llm_params": " \n\tcreate table llm_params\n(\n    id              int                not null auto_increment,\n    llm_name        text               not null, \n\tllm_type \t\ttext  \t\t\t   null , \n    description     text               null,\n    base_url        text               not null,\n    resource_name   text               not null,\n    token           text               not null,\n    embedding_model text               not null,\n    model           text               not null,\n    api_version     text               not null,\n    temperature     float default 0    not null,\n    max_token       int   default 4096 not null,\n    primary key (id，llm_name),\n    unique (llm_name)\n)\n",
		"tenant": "\nCREATE TABLE  tenant  (\n     tenant_Id NVARCHAR(200) NOT NULL,\n     password NVARCHAR(20) NOT NULL,\n     app_Key NVARCHAR(255) NULL,\n     available BOOLEAN NOT NULL DEFAULT TRUE,\n     remark  TEXT NULL,\n    PRIMARY KEY (tenant_Id), \n\n)\n\n",
		"tenant_llm": "\n \tcreate table tenant_llm\n(\n    id        int auto_increment\n        primary key,\n    tenant_id text not null,\n    llm_name  text not null,\n    constraint tenant_llm_id_llm_name_tenant_id\n        unique (tenant_id, llm_name) using hash\n)\n",
		"tenant_params": "\ncreate table tenant_params\n(\n    id        int auto_increment\n        primary key,\n    tenant_id varchar(200) not null,\n    params    text         not null,\n    constraint tenant_params_pk\n        unique (tenant_id)\n)\n"
	},
	"schema": "dsh",
	"table": "",
	"where_conditions": "",
	"where_params": null
}
2025-06-12T18:22:54.261+08:00 [DEBU] {18a75c238044481824af877045cfc148} [tenants/internal/logic/messageQ.(*sMessageQ).Send] messageQ.go:63: Send: route key "mariadb.tenant", action "createSchema" , data: {
	"data": null,
	"schema": "dsh",
	"table": "",
	"where_conditions": "",
	"where_params": null
}
2025-06-12T18:22:54.262+08:00 [DEBU] {18a75c238044481824af877045cfc148} [tenants/internal/logic/messageQ.(*sMessageQ).Send] messageQ.go:63: Send: route key "mariadb.tenant", action "createTable" , data: {
	"data": {
		"llm_params": " \n\tcreate table llm_params\n(\n    id              int                not null auto_increment,\n    llm_name        text               not null, \n\tllm_type \t\ttext  \t\t\t   null , \n    description     text               null,\n    base_url        text               not null,\n    resource_name   text               not null,\n    token           text               not null,\n    embedding_model text               not null,\n    model           text               not null,\n    api_version     text               not null,\n    temperature     float default 0    not null,\n    max_token       int   default 4096 not null,\n    primary key (id，llm_name),\n    unique (llm_name)\n)\n",
		"tenant": "\nCREATE TABLE  tenant  (\n     tenant_Id NVARCHAR(200) NOT NULL,\n     password NVARCHAR(20) NOT NULL,\n     app_Key NVARCHAR(255) NULL,\n     available BOOLEAN NOT NULL DEFAULT TRUE,\n     remark  TEXT NULL,\n    PRIMARY KEY (tenant_Id), \n\n)\n\n",
		"tenant_llm": "\n \tcreate table tenant_llm\n(\n    id        int auto_increment\n        primary key,\n    tenant_id text not null,\n    llm_name  text not null,\n    constraint tenant_llm_id_llm_name_tenant_id\n        unique (tenant_id, llm_name) using hash\n)\n",
		"tenant_params": "\ncreate table tenant_params\n(\n    id        int auto_increment\n        primary key,\n    tenant_id varchar(200) not null,\n    params    text         not null,\n    constraint tenant_params_pk\n        unique (tenant_id)\n)\n"
	},
	"schema": "dsh",
	"table": "",
	"where_conditions": "",
	"where_params": null
}
2025-06-12T18:28:11.105+08:00 [DEBU] {18a75c238044481824af877045cfc148} [tenants/internal/logic/messageQ.(*sMessageQ).Send] messageQ.go:63: Send: route key "mariadb.tenant", action "createSchema" , data: {
	"data": null,
	"schema": "dsh",
	"table": "",
	"where_conditions": "",
	"where_params": null
}
2025-06-12T18:28:11.106+08:00 [DEBU] {18a75c238044481824af877045cfc148} [tenants/internal/logic/messageQ.(*sMessageQ).Send] messageQ.go:63: Send: route key "mariadb.tenant", action "createTable" , data: {
	"data": {
		"llm_params": " \n\tcreate table llm_params\n(\n    id              int                not null auto_increment,\n    llm_name        text               not null, \n\tllm_type \t\ttext  \t\t\t   null , \n    description     text               null,\n    base_url        text               not null,\n    resource_name   text               not null,\n    token           text               not null,\n    embedding_model text               not null,\n    model           text               not null,\n    api_version     text               not null,\n    temperature     float default 0    not null,\n    max_token       int   default 4096 not null,\n    primary key (id，llm_name),\n    unique (llm_name)\n)\n",
		"tenant": "\nCREATE TABLE  tenant  (\n     tenant_Id NVARCHAR(200) NOT NULL,\n     password NVARCHAR(20) NOT NULL,\n     app_Key NVARCHAR(255) NULL,\n     available BOOLEAN NOT NULL DEFAULT TRUE,\n     remark  TEXT NULL,\n    PRIMARY KEY (tenant_Id)  \n\n)\n\n",
		"tenant_llm": "\n \tcreate table tenant_llm\n(\n    id        int auto_increment\n        primary key,\n    tenant_id text not null,\n    llm_name  text not null,\n    constraint tenant_llm_id_llm_name_tenant_id\n        unique (tenant_id, llm_name) using hash\n)\n",
		"tenant_params": "\ncreate table tenant_params\n(\n    id        int auto_increment\n        primary key,\n    tenant_id varchar(200) not null,\n    params    text         not null,\n    constraint tenant_params_pk\n        unique (tenant_id)\n)\n"
	},
	"schema": "dsh",
	"table": "",
	"where_conditions": "",
	"where_params": null
}
2025-06-12T18:28:55.253+08:00 [DEBU] {18a75c238044481824af877045cfc148} [tenants/internal/logic/messageQ.(*sMessageQ).Send] messageQ.go:63: Send: route key "mariadb.tenant", action "createSchema" , data: {
	"data": null,
	"schema": "dsh",
	"table": "",
	"where_conditions": "",
	"where_params": null
}
2025-06-12T18:28:55.255+08:00 [DEBU] {18a75c238044481824af877045cfc148} [tenants/internal/logic/messageQ.(*sMessageQ).Send] messageQ.go:63: Send: route key "mariadb.tenant", action "createTable" , data: {
	"data": {
		"llm_params": " \n\tcreate table llm_params\n(\n    id              int                not null auto_increment,\n    llm_name        text               not null, \n\tllm_type \t\ttext  \t\t\t   null , \n    description     text               null,\n    base_url        text               not null,\n    resource_name   text               not null,\n    token           text               not null,\n    embedding_model text               not null,\n    model           text               not null,\n    api_version     text               not null,\n    temperature     float default 0    not null,\n    max_token       int   default 4096 not null,\n    primary key (id，llm_name),\n    unique (llm_name)\n)\n",
		"tenant": "\nCREATE TABLE tenant (\n    tenant_Id NVARCHAR(200) NOT NULL,\n    password NVARCHAR(20) NOT NULL,\n    app_Key NVARCHAR(255) NULL,\n    available BOOLEAN NOT NULL DEFAULT TRUE,\n    remark TEXT NULL,\n    PRIMARY KEY (tenant_Id)\n);\n\n",
		"tenant_llm": "\n \tcreate table tenant_llm\n(\n    id        int auto_increment\n        primary key,\n    tenant_id text not null,\n    llm_name  text not null,\n    constraint tenant_llm_id_llm_name_tenant_id\n        unique (tenant_id, llm_name) using hash\n)\n",
		"tenant_params": "\ncreate table tenant_params\n(\n    id        int auto_increment\n        primary key,\n    tenant_id varchar(200) not null,\n    params    text         not null,\n    constraint tenant_params_pk\n        unique (tenant_id)\n)\n"
	},
	"schema": "dsh",
	"table": "",
	"where_conditions": "",
	"where_params": null
}
2025-06-12T18:31:51.250+08:00 [DEBU] {18a75c238044481824af877045cfc148} [tenants/internal/logic/messageQ.(*sMessageQ).Send] messageQ.go:63: Send: route key "mariadb.tenant", action "createSchema" , data: {
	"data": null,
	"schema": "dsh",
	"table": "",
	"where_conditions": "",
	"where_params": null
}
2025-06-12T18:31:51.250+08:00 [DEBU] {18a75c238044481824af877045cfc148} [tenants/internal/logic/messageQ.(*sMessageQ).Send] messageQ.go:63: Send: route key "mariadb.tenant", action "createTable" , data: {
	"data": {
		"llm_params": " \n\tcreate table llm_params\n(\n    id              int                not null auto_increment,\n    llm_name        text               not null, \n\tllm_type \t\ttext  \t\t\t   null , \n    description     text               null,\n    base_url        text               not null,\n    resource_name   text               not null,\n    token           text               not null,\n    embedding_model text               not null,\n    model           text               not null,\n    api_version     text               not null,\n    temperature     float default 0    not null,\n    max_token       int   default 4096 not null,\n    primary key (id，llm_name),\n    unique (llm_name)\n)\n",
		"tenant": "\nCREATE TABLE tenant (\n    tenant_Id NVARCHAR(200) NOT NULL,\n    password NVARCHAR(20) NOT NULL,\n    app_Key NVARCHAR(255) NULL,\n    available BOOLEAN NOT NULL DEFAULT TRUE,\n    remark TEXT NULL,\n    PRIMARY KEY (tenant_Id)\n);\n\n",
		"tenant_llm": "\n \tcreate table tenant_llm\n(\n    id        int auto_increment primary key,\n    tenant_id varchar(255) not null,\n    llm_name  varchar(255) not null,\n    unique key tenant_llm_tenant_id_llm_name (tenant_id, llm_name)\n);\n",
		"tenant_params": "\ncreate table tenant_params\n(\n    id        int auto_increment\n        primary key,\n    tenant_id varchar(200) not null,\n    params    text         not null,\n    constraint tenant_params_pk\n        unique (tenant_id)\n)\n"
	},
	"schema": "dsh",
	"table": "",
	"where_conditions": "",
	"where_params": null
}
2025-06-12T18:34:05.142+08:00 [DEBU] {18a75c238044481824af877045cfc148} [tenants/internal/logic/messageQ.(*sMessageQ).Send] messageQ.go:63: Send: route key "mariadb.tenant", action "createSchema" , data: {
	"data": null,
	"schema": "dsh",
	"table": "",
	"where_conditions": "",
	"where_params": null
}
2025-06-12T18:34:05.143+08:00 [DEBU] {18a75c238044481824af877045cfc148} [tenants/internal/logic/messageQ.(*sMessageQ).Send] messageQ.go:63: Send: route key "mariadb.tenant", action "createTable" , data: {
	"data": {
		"llm_params": " \ncreate table llm_params\n(\n    id              int auto_increment primary key,  \n    llm_name        varchar(255)       not null unique, \n    llm_type        varchar(100)       null , \n    description     text               null,\n    base_url        varchar(500)       not null,\n    resource_name   varchar(255)       not null,\n    token           text               not null,\n    embedding_model varchar(255)       not null,\n    model           varchar(255)       not null,\n    api_version     varchar(50)        not null,\n    temperature     float default 0    not null,\n    max_token       int   default 4096 not null\n) \n",
		"tenant": "\nCREATE TABLE tenant (\n    tenant_Id NVARCHAR(200) NOT NULL,\n    password NVARCHAR(20) NOT NULL,\n    app_Key NVARCHAR(255) NULL,\n    available BOOLEAN NOT NULL DEFAULT TRUE,\n    remark TEXT NULL,\n    PRIMARY KEY (tenant_Id)\n);\n\n",
		"tenant_llm": "\n \tcreate table tenant_llm\n(\n    id        int auto_increment primary key,\n    tenant_id varchar(255) not null,\n    llm_name  varchar(255) not null,\n    unique key tenant_llm_tenant_id_llm_name (tenant_id, llm_name)\n);\n",
		"tenant_params": "\ncreate table tenant_params\n(\n    id        int auto_increment\n        primary key,\n    tenant_id varchar(200) not null,\n    params    text         not null,\n    constraint tenant_params_pk\n        unique (tenant_id)\n)\n"
	},
	"schema": "dsh",
	"table": "",
	"where_conditions": "",
	"where_params": null
}
