2023-11-27 13:37:53.302 [DEBU] {e0b1855d37649b1790a2d611aa0457e7} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/list Body:
2023-11-27 13:37:53.304 [DEBU] {e0b1855d37649b1790a2d611aa0457e7} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-{"code":0,"data":{"tenants":[{"app_Key":"d06a3c67-a88a-4712-8d5b-a572e9a68392","available":true,"password":"1234abcd","tenant_Id":"Tenant04"}]},"message":"success"}
2023-11-27 13:39:13.191 [DEBU] {70d10dfa49649b1791a2d611af32ec22} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=import
2023-11-27 13:39:13.230 [DEBU] {70d10dfa49649b1791a2d611af32ec22} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-exception recovered: interface conversion: interface {} is nil, not string
2023-11-27 13:40:54.559 [DEBU] {98a4429461649b1792a2d6112847f3a1} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=import
2023-11-27 13:40:54.591 [DEBU] {98a4429461649b1792a2d6112847f3a1} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-exception recovered: interface conversion: interface {} is nil, not string
2023-11-27 13:42:19.060 [DEBU] {3086584175649b1793a2d611c2a64730} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=import
2023-11-27 13:43:17.849 [DEBU] {3086584175649b1793a2d611c2a64730} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-exception recovered: interface conversion: interface {} is nil, not string
2023-11-27 13:43:22.961 [DEBU] {f0ddeb2284649b1794a2d61123fc450c} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=import
2023-11-27 13:43:54.241 [DEBU] {f0ddeb2284649b1794a2d61123fc450c} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-exception recovered: interface conversion: interface {} is nil, not string
2023-11-27 13:44:00.049 [DEBU] {68b1f4c58c649b1795a2d611e89dd489} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=import
2023-11-27 13:45:49.272 [DEBU] {68b1f4c58c649b1795a2d611e89dd489} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-exception recovered: interface conversion: interface {} is nil, not string
2023-11-27 13:45:56.695 [DEBU] {80ecd1eda7649b1796a2d611061ffb10} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:action=import&service=quizto
2023-11-27 13:46:14.156 [DEBU] {80ecd1eda7649b1796a2d611061ffb10} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-Unauthorized
2023-11-27 13:52:15.599 [DEBU] {e0e24f2500659b1797a2d61177f75229} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/gettokens Body:{"tenant_id": "Tenant04", "password": "1234abcd", "app_key": "d06a3c67-a88a-4712-8d5b-a572e9a68392"}
2023-11-27 13:52:15.606 [DEBU] {e0e24f2500659b1797a2d61177f75229} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-{"code":0,"message":"success","access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZW5hbnRfaWQiOiJUZW5hbnQwNCIsInBhc3N3b3JkIjoiMTIzNGFiY2QiLCJhcHBfS2V5IjoiZDA2YTNjNjctYTg4YS00NzEyLThkNWItYTU3MmU5YTY4MzkyIiwiZXhwIjoxNzAxMTUwNzM1LCJpc3MiOiJBaTMifQ.7Kjo2DJNIhTcng71UBdBnX4138mFaSm19kqfTBhfQmE","refresh_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZW5hbnRfaWQiOiJUZW5hbnQwNCIsInBhc3N3b3JkIjoiMTIzNGFiY2QiLCJhcHBfS2V5IjoiZDA2YTNjNjctYTg4YS00NzEyLThkNWItYTU3MmU5YTY4MzkyIiwiZXhwIjoxNzAxNjY5MTM1LCJpc3MiOiJBaTMifQ.L3wfPz2RUzzygmm4eXuil465SlrPgbKhK2fyRR3xSM4"}
2023-11-27 13:52:48.838 [DEBU] {a830fce207659b1798a2d6119891c778} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=import
2023-11-27 13:52:48.840 [DEBU] {a830fce207659b1798a2d6119891c778} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:01:13.391 [DEBU] {f863525c7d659b1799a2d61167639ee4} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=import
2023-11-27 14:01:13.396 [DEBU] {f863525c7d659b1799a2d61167639ee4} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:02:45.412 [DEBU] {d0ac94ca92659b179aa2d611570b86f6} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=getsourcever
2023-11-27 14:02:45.414 [DEBU] {d0ac94ca92659b179aa2d611570b86f6} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:07:26.471 [DEBU] {8001073ad4659b179ba2d611986134b9} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/gettokens Body:{"tenant_id": "Tenant04", "password": "1234abcd", "app_key": "d06a3c67-a88a-4712-8d5b-a572e9a68392"}
2023-11-27 14:07:26.478 [DEBU] {8001073ad4659b179ba2d611986134b9} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-{"code":0,"message":"success","access_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZW5hbnRfaWQiOiJUZW5hbnQwNCIsInBhc3N3b3JkIjoiMTIzNGFiY2QiLCJhcHBfS2V5IjoiZDA2YTNjNjctYTg4YS00NzEyLThkNWItYTU3MmU5YTY4MzkyIiwiZXhwIjoxNzAxMTUxNjQ2LCJpc3MiOiJBaTMifQ.2ifon6bXoIO2NiEyzzThHWazb5fWSJojd-5-kgdq7yk","refresh_token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0ZW5hbnRfaWQiOiJUZW5hbnQwNCIsInBhc3N3b3JkIjoiMTIzNGFiY2QiLCJhcHBfS2V5IjoiZDA2YTNjNjctYTg4YS00NzEyLThkNWItYTU3MmU5YTY4MzkyIiwiZXhwIjoxNzAxNjcwMDQ2LCJpc3MiOiJBaTMifQ.VapdYUKw8HFjqR3kLWGWkWm59LAbHgVX_XyXwUIviEI"}
2023-11-27 14:07:28.977 [DEBU] {c04380cfd4659b179ca2d611054d4f16} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=getsourcever
2023-11-27 14:07:28.979 [DEBU] {c04380cfd4659b179ca2d611054d4f16} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:08:57.298 [DEBU] {301a1661e9659b179da2d611d07b4f99} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:action=getsourcever&service=quizto
2023-11-27 14:08:57.303 [DEBU] {301a1661e9659b179da2d611d07b4f99} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:09:04.718 [DEBU] {906f7c1beb659b179ea2d61190a6bbba} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=getsourcever
2023-11-27 14:09:04.722 [DEBU] {906f7c1beb659b179ea2d61190a6bbba} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:09:04.759 [DEBU] {f8a4041eeb659b179fa2d61194aceb32} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=maintain
2023-11-27 14:09:04.759 [DEBU] {f8a4041eeb659b179fa2d61194aceb32} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:09:04.844 [DEBU] {10d41c23eb659b17a0a2d611c6dffffd} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=maintain
2023-11-27 14:09:04.844 [DEBU] {10d41c23eb659b17a0a2d611c6dffffd} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:09:04.874 [DEBU] {a816e624eb659b17a1a2d61167b7d028} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=maintain
2023-11-27 14:09:04.875 [DEBU] {a816e624eb659b17a1a2d61167b7d028} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:09:04.908 [DEBU] {d09df326eb659b17a2a2d611c5ae5c8e} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=maintain
2023-11-27 14:09:04.909 [DEBU] {d09df326eb659b17a2a2d611c5ae5c8e} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:09:04.944 [DEBU] {c0d5ec28eb659b17a3a2d61102257ff4} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:action=maintain&service=quizto
2023-11-27 14:09:04.944 [DEBU] {c0d5ec28eb659b17a3a2d61102257ff4} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:09:04.974 [DEBU] {98dcd92aeb659b17a4a2d6114bb7b166} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=maintain
2023-11-27 14:09:04.974 [DEBU] {98dcd92aeb659b17a4a2d6114bb7b166} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:09:05.000 [DEBU] {58f7702ceb659b17a5a2d6112bb6c684} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=maintain
2023-11-27 14:09:05.001 [DEBU] {58f7702ceb659b17a5a2d6112bb6c684} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:09:05.033 [DEBU] {88135f2eeb659b17a6a2d611de3d4660} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=maintain
2023-11-27 14:09:05.033 [DEBU] {88135f2eeb659b17a6a2d611de3d4660} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:09:49.880 [DEBU] {50745b9ff5659b17a7a2d611b02e07a4} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:service=quizto&action=getsourcever
2023-11-27 14:09:49.882 [DEBU] {50745b9ff5659b17a7a2d611b02e07a4} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
2023-11-27 14:09:49.936 [DEBU] {387ddaa2f5659b17a8a2d6115c65718a} [tenants/internal/cmd.MiddleHandler] cmd.go:36: Request-Uri: /v1/tenants/auth Body:action=maintain&service=quizto
2023-11-27 14:09:49.937 [DEBU] {387ddaa2f5659b17a8a2d6115c65718a} [tenants/internal/cmd.MiddleHandler] cmd.go:38: Response-OK
