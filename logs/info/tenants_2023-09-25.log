2023-09-25 13:34:22.567 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 13:57:53.259 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 14:59:15.160 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 15:48:14.215 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 15:49:55.772 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 15:51:56.052 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 15:52:17.058 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 16:00:06.865 [INFO] {c8d9b09f5d158817d032ff073c17596e} [tenants/internal/service.(*Tenant).AuthTenant] tenant_service.go:486: Authenticate for tenant admin
2023-09-25 16:06:00.969 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 16:09:01.431 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 16:17:23.988 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 16:17:55.715 [INFO] {10e383aa5f168817ccf1eb6dfa832b79} [tenants/internal/service.(*Tenant).CreateTenant] tenant_service.go:292: Create tenant with id: Tenant01  ... 
2023-09-25 16:21:06.662 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 16:21:19.914 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 16:21:44.191 [INFO] {b872f10f96168817af5d104687d352eb} [tenants/internal/service.(*Tenant).CreateTenant] tenant_service.go:292: Create tenant with id: Tenant01  ... 
2023-09-25 16:22:44.929 [INFO] {6076fd72750d881766120117e469d0e0} Connect to  localhost:8070  
2023-09-25 16:23:47.582 [INFO] {10031a12b51688175e0f2831fe3a6272} Connect to  localhost:8070  
2023-09-25 16:24:41.133 [INFO] {3813413cb8168817ba9059444472709c} [tenants/internal/service.(*Tenant).CreateTenant] tenant_service.go:292: Create tenant with id: Tenant01  ... 
2023-09-25 16:38:10.233 [INFO] {a0e768ad7d178817137d5911113ff3ff} Connect to  localhost:8070  
2023-09-25 16:40:41.691 [INFO] {c875b531a1178817554a0353b28d6c35} Connect to  localhost:8070  
2023-09-25 16:41:18.524 [INFO] {e8399e74a51788173213dc7bdddbaeb1} [tenants/internal/service.(*Tenant).CreateTenant] tenant_service.go:307: Create tenant with id: Tenant01  ... 
2023-09-25 16:43:15.687 [INFO] {b81eb19db91788173313dc7b29050ba8} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:389: List all tenants... 
2023-09-25 16:51:11.695 [INFO] {4815014e3218881786ce960c535994e4} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:389: List all tenants... 
2023-09-25 16:52:33.882 [INFO] {08c47d8b4518881787ce960c4b1ea1ad} [tenants/internal/service.(*Tenant).GenNewAppKeyForTenant] tenant_service.go:464: Generate new app key for tenant Tenant01
2023-09-25 16:59:59.818 [INFO] {c0e9d4d8ae1888179f38b238cbf4ebfd} Connect to  localhost:8070  
2023-09-25 17:00:06.801 [INFO] {90a5d374b01888172f70e356d2f901c3} Connect to  localhost:8070  
2023-09-25 17:00:40.393 [INFO] {b0163d3cb718881788ce960c0d57db6b} [tenants/internal/service.(*Tenant).GenNewAppKeyForTenant] tenant_service.go:464: Generate new app key for tenant Tenant01
2023-09-25 17:01:03.526 [INFO] {20315a5cbd18881789ce960c516a7604} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:389: List all tenants... 
2023-09-25 17:06:41.326 [INFO] {d81539870c1988178ace960cbb2a39ab} [tenants/internal/service.(*Tenant).AuthTenant] tenant_service.go:501: Authenticate for tenant Tenant01
2023-09-25 17:07:08.503 [INFO] {a8ed9b3f121988178bce960c04010740} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:389: List all tenants... 
2023-09-25 17:10:19.982 [INFO] {380303303d1988178cce960c307789b7} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:389: List all tenants... 
2023-09-25 17:10:36.647 [INFO] {c8d4be58421988178dce960c020e83a6} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:389: List all tenants... 
2023-09-25 17:10:55.225 [INFO] {1853624d451988178ece960caf79b29a} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:389: List all tenants... 
2023-09-25 17:15:49.411 [INFO] {a8b180ef8b198817b2422214fe5a1304} Connect to  localhost:8070  
2023-09-25 17:18:23.170 [INFO] {a04e0db9af198817ebe958564c966314} Connect to  localhost:8070  
2023-09-25 17:18:25.613 [INFO] {a04e0db9af198817ebe958564c966314} [{"dataType":["text"],"indexFilterable":true,"indexSearchable":true,"moduleConfig":{"text2vec-openai":{"skip":false,"vectorizePropertyName":false}},"name":"tenant_Id","tokenization":"word"},{"dataType":["text"],"indexFilterable":true,"indexSearchable":true,"moduleConfig":{"text2vec-openai":{"skip":false,"vectorizePropertyName":false}},"name":"password","tokenization":"word"},{"dataType":["uuid"],"indexFilterable":true,"indexSearchable":false,"moduleConfig":{"text2vec-openai":{"skip":false,"vectorizePropertyName":false}},"name":"app_Key"},{"dataType":["boolean"],"indexFilterable":true,"indexSearchable":false,"moduleConfig":{"text2vec-openai":{"skip":false,"vectorizePropertyName":false}},"name":"available"}]
2023-09-25 17:19:44.587 [INFO] {588927adc219881780da1177f58d15f3} Connect to  localhost:8070  
2023-09-25 17:19:46.868 [INFO] {588927adc219881780da1177f58d15f3} [
	{
		"dataType": [
			"text"
		],
		"indexFilterable": true,
		"indexSearchable": true,
		"moduleConfig": {
			"text2vec-openai": {
				"skip": false,
				"vectorizePropertyName": false
			}
		},
		"name": "tenant_Id",
		"tokenization": "word"
	},
	{
		"dataType": [
			"text"
		],
		"indexFilterable": true,
		"indexSearchable": true,
		"moduleConfig": {
			"text2vec-openai": {
				"skip": false,
				"vectorizePropertyName": false
			}
		},
		"name": "password",
		"tokenization": "word"
	},
	{
		"dataType": [
			"uuid"
		],
		"indexFilterable": true,
		"indexSearchable": false,
		"moduleConfig": {
			"text2vec-openai": {
				"skip": false,
				"vectorizePropertyName": false
			}
		},
		"name": "app_Key"
	},
	{
		"dataType": [
			"boolean"
		],
		"indexFilterable": true,
		"indexSearchable": false,
		"moduleConfig": {
			"text2vec-openai": {
				"skip": false,
				"vectorizePropertyName": false
			}
		},
		"name": "available"
	}
]
2023-09-25 17:23:45.244 [INFO] {88a117bafa19881702e38347d04c6579} Connect to  localhost:8070  
2023-09-25 17:24:04.610 [INFO] {4000b06fff1988178fce960c59305fdd} [tenants/internal/service.(*Tenant).CreateTenant] tenant_service.go:309: Create tenant with id: Tenant02  ... 
2023-09-25 17:25:22.265 [INFO] {d8e76fce101a881790ce960ce57eaac0} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:391: List all tenants... 
2023-09-25 17:26:37.456 [INFO] {00b77d62221a881791ce960c45141478} [tenants/internal/service.(*Tenant).GenNewAppKeyForTenant] tenant_service.go:466: Generate new app key for tenant Tenant02
2023-09-25 17:28:55.007 [INFO] {c0e3778d421a881792ce960c95e6127d} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:391: List all tenants... 
2023-09-25 17:33:57.049 [INFO] {5847ac2a891a8817b04e3631d5c4ba7a} Connect to  localhost:8070  
2023-09-25 17:34:02.450 [INFO] {f037af6b8a1a8817e005cd2e072fe52c} Connect to  localhost:8070  
2023-09-25 17:34:28.238 [INFO] {a0410998901a8817cd1a9871a234cda4} [tenants/internal/service.(*Tenant).CreateTenant] tenant_service.go:309: Create tenant with id: Tenant03  ... 
2023-09-25 17:34:56.521 [INFO] {80c90376961a8817ce1a9871dfbd4268} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:391: List all tenants... 
2023-09-25 17:35:14.614 [INFO] {f8c064ce9a1a8817cf1a987163c7c6bd} [tenants/internal/service.(*Tenant).GenNewAppKeyForTenant] tenant_service.go:466: Generate new app key for tenant Tenant03
2023-09-25 17:35:25.528 [INFO] {d8b3d76c9d1a8817d01a9871d7188a77} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:391: List all tenants... 
2023-09-25 17:36:13.760 [INFO] {704d7735a91a8817d11a98718155ea13} [tenants/internal/service.(*Tenant).AuthTenant] tenant_service.go:508: Authenticate for tenant Tenant03
2023-09-25 17:36:25.049 [INFO] {b017f7d5ab1a8817d21a987117a92ed1} [tenants/internal/service.(*Tenant).AuthTenant] tenant_service.go:508: Authenticate for tenant Tenant03
2023-09-25 17:45:09.372 [INFO] {d8e980b4251b8817ffabdd67cd24724d} Connect to  localhost:8070  
2023-09-25 17:47:36.969 [INFO] {88fb7512481b8817e180fb701185feb4} Connect to  localhost:8070  
2023-09-25 17:48:11.578 [INFO] {1080bc484b1b8817d61a987198888e48} [tenants/internal/service.(*Tenant).UpdateTenant] tenant_service.go:359: Update tenant Tenant03 with new password ... 
2023-09-25 17:57:39.960 [INFO] {e0b67c75d41b8817e9f2a06a86f8d702} Connect to  localhost:8070  
2023-09-25 17:58:39.597 [INFO] {88cc50e5e11b8817d71a9871cd2ea944} [tenants/internal/service.(*Tenant).UpdateTenant] tenant_service.go:360: Update tenant Tenant03 with new password ... 
2023-09-25 18:00:26.847 [INFO] {781c9952fb1b8817cad6bd02d6449086} Connect to  localhost:8070  
2023-09-25 18:00:37.652 [INFO] {50ba857ffd1b8817d81a98716ffd964c} [tenants/internal/service.(*Tenant).UpdateTenant] tenant_service.go:360: Update tenant Tenant03 with new password ... 
2023-09-25 18:01:36.154 [INFO] {f8f9de2b0b1c8817d91a98716536df4f} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:397: List all tenants... 
2023-09-25 18:04:46.100 [INFO] {2824e5e4371c8817da1a98715b78681c} [tenants/internal/service.(*Tenant).AuthTenant] tenant_service.go:514: Authenticate for tenant Tenant02
2023-09-25 18:07:00.540 [INFO] {30f31432571c8817db1a9871fe260a14} [tenants/internal/service.(*Tenant).AuthTenant] tenant_service.go:514: Authenticate for tenant admin
2023-09-25 18:08:14.435 [INFO] {60181b66681c8817dc1a9871edf7acdc} [tenants/internal/service.(*Tenant).AuthTenant] tenant_service.go:514: Authenticate for tenant admin
2023-09-25 18:08:31.802 [INFO] {c0a8cf716c1c8817dd1a98711c169c9e} [tenants/internal/service.(*Tenant).AuthTenant] tenant_service.go:514: Authenticate for tenant admin
2023-09-25 18:11:05.386 [INFO] {a85ce3de811c8817de1a9871c897b263} [tenants/internal/service.(*Tenant).AuthTenant] tenant_service.go:514: Authenticate for tenant admin
2023-09-25 18:15:59.367 [INFO] {20d7a76bc01c8817df1a9871eaeb1c09} [tenants/internal/service.(*Tenant).DisableTenant] tenant_service.go:337: Disable tenant Tenant02 ... 
