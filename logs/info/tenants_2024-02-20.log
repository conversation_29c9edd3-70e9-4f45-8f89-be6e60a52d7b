2024-02-20 17:02:10.018 [INFO] {00dc355fae86b517ab78b9112665e793} Connect weaviate: [192.168.4.67:8070]  
2024-02-20 17:02:10.069 [INFO] {00dc355fae86b517ab78b9112665e793} List all tenants... 
2024-02-20 17:02:10.109 [INFO] {00dc355fae86b517ab78b9112665e793} llm check interval: 1m
2024-02-20 17:02:10.109 [INFO] {00dc355fae86b517ab78b9112665e793} load tenant llm to cache...
2024-02-20 17:10:22.926 [INFO] {0030459f2387b5171a68400443269449} Connect weaviate: [192.168.4.67:8070]  
2024-02-20 17:10:22.957 [INFO] {0030459f2387b5171a68400443269449} List all tenants... 
2024-02-20 17:10:22.996 [INFO] {0030459f2387b5171a68400443269449} llm check interval: 1m
2024-02-20 17:10:22.996 [INFO] {0030459f2387b5171a68400443269449} load tenant llm to cache...
2024-02-20 17:11:35.002 [INFO] {10def5cf3387b51752f62524455dfaa2} Connect weaviate: [192.168.4.67:8070]  
2024-02-20 17:11:35.040 [INFO] {10def5cf3387b51752f62524455dfaa2} List all tenants... 
2024-02-20 17:11:35.094 [INFO] {10def5cf3387b51752f62524455dfaa2} llm check interval: 1m
2024-02-20 17:11:35.094 [INFO] {10def5cf3387b51752f62524455dfaa2} load tenant llm to cache...
2024-02-20 17:11:35.114 [INFO] {10def5cf3387b51752f62524455dfaa2} Check database and tables ... 
2024-02-20 17:12:35.075 [INFO] {10def5cf3387b51752f62524455dfaa2} [tenants/internal/logic/datastore.(*sDataStore).WriteLLMParams] datastore.go:241: Write LLM Params :   {
	"api_version": "2023-12-01-preview",
	"base_url": "https://qbibotopenai.openai.azure.com/",
	"description": "AOAI of Qbibot",
	"embedding_model": "text-embedding-ada-002",
	"llm_name": "QbiBotOpenAI",
	"max_token": 4096,
	"model": "gpt-35-turbo",
	"resource_name": "qbibotopenai",
	"temperature": 0.5,
	"token": "050928bf5c1b47e5b3a2da43b164f383"
}
