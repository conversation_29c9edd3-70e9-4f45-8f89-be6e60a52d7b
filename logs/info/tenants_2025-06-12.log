2025-06-12T17:42:03.273+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} llm check interval: 10s
2025-06-12T17:42:03.273+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} load tenant llm to cache...
2025-06-12T17:42:03.326+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} List all tenants... 
2025-06-12T17:42:58.602+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} llm check interval: 10s
2025-06-12T17:42:58.602+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} load tenant llm to cache...
2025-06-12T17:42:58.632+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} List all tenants... 
2025-06-12T17:43:05.812+08:00 [INFO] {78b4633c5442481873ecd32a365baa50} llm check interval: 10s
2025-06-12T17:43:05.812+08:00 [INFO] {78b4633c5442481873ecd32a365baa50} load tenant llm to cache...
2025-06-12T17:43:05.838+08:00 [INFO] {78b4633c5442481873ecd32a365baa50} List all tenants... 
2025-06-12T17:46:13.211+08:00 [INFO] {c043b23b7f424818615d89063f49a47f} llm check interval: 10s
2025-06-12T17:46:13.211+08:00 [INFO] {c043b23b7f424818615d89063f49a47f} load tenant llm to cache...
2025-06-12T17:46:13.215+08:00 [INFO] {c043b23b7f424818615d89063f49a47f} List all tenants... 
2025-06-12T17:58:21.711+08:00 [INFO] {a0aafc27294348188a062d76cfee60e4} llm check interval: 10s
2025-06-12T17:58:21.711+08:00 [INFO] {a0aafc27294348188a062d76cfee60e4} load tenant llm to cache...
2025-06-12T17:58:21.714+08:00 [INFO] {a0aafc27294348188a062d76cfee60e4} List all tenants... 
2025-06-12T18:02:54.728+08:00 [INFO] {a0aafc27294348188a062d76cfee60e4} llm check interval: 10s
2025-06-12T18:02:54.728+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} llm check interval: 10s
2025-06-12T18:02:54.728+08:00 [INFO] {a0aafc27294348188a062d76cfee60e4} load tenant llm to cache...
2025-06-12T18:02:54.728+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} load tenant llm to cache...
2025-06-12T18:04:46.367+08:00 [INFO] {a0aafc27294348188a062d76cfee60e4} llm check interval: 10s
2025-06-12T18:04:46.367+08:00 [INFO] {a0aafc27294348188a062d76cfee60e4} load tenant llm to cache...
2025-06-12T18:04:46.367+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} llm check interval: 10s
2025-06-12T18:04:46.367+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} load tenant llm to cache...
2025-06-12T18:06:09.780+08:00 [INFO] {207e6837964348187625191739e3a631} llm check interval: 10s
2025-06-12T18:06:09.780+08:00 [INFO] {207e6837964348187625191739e3a631} load tenant llm to cache...
2025-06-12T18:06:09.790+08:00 [INFO] {207e6837964348187625191739e3a631} List all tenants... 
2025-06-12T18:06:50.086+08:00 [INFO] {f8d6c99b9f4348185962bd43548b419e} llm check interval: 10s
2025-06-12T18:06:50.087+08:00 [INFO] {f8d6c99b9f4348185962bd43548b419e} load tenant llm to cache...
2025-06-12T18:06:50.094+08:00 [INFO] {f8d6c99b9f4348185962bd43548b419e} List all tenants... 
2025-06-12T18:10:29.714+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} llm check interval: 10s
2025-06-12T18:10:29.715+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} load tenant llm to cache...
2025-06-12T18:10:29.714+08:00 [INFO] {f8d6c99b9f4348185962bd43548b419e} llm check interval: 10s
2025-06-12T18:10:29.715+08:00 [INFO] {f8d6c99b9f4348185962bd43548b419e} load tenant llm to cache...
2025-06-12T18:10:41.135+08:00 [INFO] {30172a65d543481866086f3e377ef3ee} llm check interval: 10s
2025-06-12T18:10:41.135+08:00 [INFO] {30172a65d543481866086f3e377ef3ee} load tenant llm to cache...
2025-06-12T18:11:42.487+08:00 [INFO] {800e86aee3434818f740fa5ca8de1ba0} llm check interval: 10s
2025-06-12T18:11:42.487+08:00 [INFO] {800e86aee3434818f740fa5ca8de1ba0} load tenant llm to cache...
2025-06-12T18:16:51.916+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} llm check interval: 10s
2025-06-12T18:16:51.917+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} load tenant llm to cache...
2025-06-12T18:16:52.830+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} [tenants/internal/logic/tenant.(*sTenant).ListTenants] tenant.go:678: List all tenants... 
2025-06-12T18:16:52.831+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} [tenants/internal/logic/datastore.(*sDataStore).checkCreateTable] datastore.go:238: Check database and tables ... 
2025-06-12T18:22:53.344+08:00 [INFO] {18a75c238044481824af877045cfc148} llm check interval: 10s
2025-06-12T18:22:53.344+08:00 [INFO] {18a75c238044481824af877045cfc148} load tenant llm to cache...
2025-06-12T18:22:54.261+08:00 [INFO] {18a75c238044481824af877045cfc148} [tenants/internal/logic/tenant.(*sTenant).ListTenants] tenant.go:678: List all tenants... 
2025-06-12T18:22:54.262+08:00 [INFO] {18a75c238044481824af877045cfc148} [tenants/internal/logic/datastore.(*sDataStore).checkCreateTable] datastore.go:238: Check database and tables ... 
2025-06-12T18:28:10.197+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} llm check interval: 10s
2025-06-12T18:28:10.197+08:00 [INFO] {18a75c238044481824af877045cfc148} llm check interval: 10s
2025-06-12T18:28:10.197+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} load tenant llm to cache...
2025-06-12T18:28:10.197+08:00 [INFO] {18a75c238044481824af877045cfc148} load tenant llm to cache...
2025-06-12T18:28:11.105+08:00 [INFO] {18a75c238044481824af877045cfc148} [tenants/internal/logic/datastore.(*sDataStore).checkCreateTable] datastore.go:238: Check database and tables ... 
2025-06-12T18:28:11.205+08:00 [INFO] {18a75c238044481824af877045cfc148} [tenants/internal/logic/tenant.(*sTenant).ListTenants] tenant.go:678: List all tenants... 
2025-06-12T18:28:54.342+08:00 [INFO] {18a75c238044481824af877045cfc148} llm check interval: 10s
2025-06-12T18:28:54.342+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} llm check interval: 10s
2025-06-12T18:28:54.342+08:00 [INFO] {18a75c238044481824af877045cfc148} load tenant llm to cache...
2025-06-12T18:28:54.342+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} load tenant llm to cache...
2025-06-12T18:28:55.253+08:00 [INFO] {18a75c238044481824af877045cfc148} [tenants/internal/logic/tenant.(*sTenant).ListTenants] tenant.go:678: List all tenants... 
2025-06-12T18:28:55.255+08:00 [INFO] {18a75c238044481824af877045cfc148} [tenants/internal/logic/datastore.(*sDataStore).checkCreateTable] datastore.go:238: Check database and tables ... 
2025-06-12T18:31:50.341+08:00 [INFO] {18a75c238044481824af877045cfc148} llm check interval: 10s
2025-06-12T18:31:50.341+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} llm check interval: 10s
2025-06-12T18:31:50.341+08:00 [INFO] {18a75c238044481824af877045cfc148} load tenant llm to cache...
2025-06-12T18:31:50.341+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} load tenant llm to cache...
2025-06-12T18:31:51.250+08:00 [INFO] {18a75c238044481824af877045cfc148} [tenants/internal/logic/tenant.(*sTenant).ListTenants] tenant.go:678: List all tenants... 
2025-06-12T18:31:51.250+08:00 [INFO] {18a75c238044481824af877045cfc148} [tenants/internal/logic/datastore.(*sDataStore).checkCreateTable] datastore.go:238: Check database and tables ... 
2025-06-12T18:34:04.219+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} llm check interval: 10s
2025-06-12T18:34:04.219+08:00 [INFO] {18a75c238044481824af877045cfc148} llm check interval: 10s
2025-06-12T18:34:04.219+08:00 [INFO] {f0408e3bf03b4818bb1e334ccbf4bb6c} load tenant llm to cache...
2025-06-12T18:34:04.219+08:00 [INFO] {18a75c238044481824af877045cfc148} load tenant llm to cache...
2025-06-12T18:34:05.142+08:00 [INFO] {18a75c238044481824af877045cfc148} [tenants/internal/logic/tenant.(*sTenant).ListTenants] tenant.go:678: List all tenants... 
2025-06-12T18:34:05.143+08:00 [INFO] {18a75c238044481824af877045cfc148} [tenants/internal/logic/datastore.(*sDataStore).checkCreateTable] datastore.go:238: Check database and tables ... 
