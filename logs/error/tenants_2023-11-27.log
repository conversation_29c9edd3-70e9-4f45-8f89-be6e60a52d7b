2023-11-27 13:33:20.921 [ERRO] {e08e2bb1f7639b17db08e0075b7516d3} status code: -1, error: check the DerivedFromError field for more information: Get "http://localhost:8070/v1/schema/Tenants": EOF 
Stack:
1.  tenants/internal/service.(*Tenant).Connect
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:524
2.  tenants/internal/service.initializeWeaviate
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:26
3.  tenants/internal/service.init.0
    /Users/<USER>/Sources/AI/tenants/internal/service/services.go:5

2023-11-27 13:33:56.757 [ERRO] {0837c7ddff639b17f7999c3548ae2fb5} status code: -1, error: check the DerivedFromError field for more information: Get "http://localhost:8070/v1/schema/Tenants": EOF 
Stack:
1.  tenants/internal/service.(*Tenant).Connect
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:524
2.  tenants/internal/service.initializeWeaviate
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:26
3.  tenants/internal/service.init.0
    /Users/<USER>/Sources/AI/tenants/internal/service/services.go:5

