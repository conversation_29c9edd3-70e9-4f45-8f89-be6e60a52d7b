2025-06-12T17:42:03.367+08:00 [ERRO] {f0408e3bf03b4818bb1e334ccbf4bb6c} request failed: Post "http://dsh.svc/v1/getContents": EOF 
Stack:
1.  tenants/internal/logic/tenant.(*sTenant).ListTenants
    /Users/<USER>/Source/AI/tenants/internal/logic/tenant/tenant.go:698
2.  tenants/internal/logic/tenant.New
    /Users/<USER>/Source/AI/tenants/internal/logic/tenant/tenant.go:43
3.  tenants/internal/logic/tenant.init.0
    /Users/<USER>/Source/AI/tenants/internal/logic/tenant/tenant.go:33

2025-06-12T17:42:58.651+08:00 [ERRO] {f0408e3bf03b4818bb1e334ccbf4bb6c} request failed: Post "http://dsh.svc/v1/getContents": EOF 
Stack:
1.  tenants/internal/logic/tenant.(*sTenant).ListTenants
    /Users/<USER>/Source/AI/tenants/internal/logic/tenant/tenant.go:698
2.  tenants/internal/logic/tenant.New
    /Users/<USER>/Source/AI/tenants/internal/logic/tenant/tenant.go:43
3.  tenants/internal/logic/tenant.init.0
    /Users/<USER>/Source/AI/tenants/internal/logic/tenant/tenant.go:33

2025-06-12T17:43:05.859+08:00 [ERRO] {78b4633c5442481873ecd32a365baa50} request failed: Post "http://dsh.svc/v1/getContents": EOF 
Stack:
1.  tenants/internal/logic/tenant.(*sTenant).ListTenants
    /Users/<USER>/Source/AI/tenants/internal/logic/tenant/tenant.go:698
2.  tenants/internal/logic/tenant.New
    /Users/<USER>/Source/AI/tenants/internal/logic/tenant/tenant.go:43
3.  tenants/internal/logic/tenant.init.0
    /Users/<USER>/Source/AI/tenants/internal/logic/tenant/tenant.go:33

2025-06-12T18:16:52.831+08:00 [ERRO] {f0408e3bf03b4818bb1e334ccbf4bb6c} [tenants/internal/logic/tenant.(*sTenant).ListTenants] tenant.go:703: request failed: Post "http://192.168.30.19:8087/v1/getContents": dial tcp 192.168.30.19:8087: connect: connection refused 
Stack:
1.  tenants/internal/logic/tenant.(*sTenant).ListTenants
    /Users/<USER>/Source/AI/tenants/internal/logic/tenant/tenant.go:703
2.  tenants/internal/logic/tenant.New.func1
    /Users/<USER>/Source/AI/tenants/internal/logic/tenant/tenant.go:46

