2023-09-26 09:32:59.944 [ERRO] {5871fdebd64e8817ed40686bfdaf3d98} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:162: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:162
2.  tenants/internal/service.(*Tenant).updateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:238
3.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:355
4.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
5.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
7.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
10. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
12. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
14. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
16. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
18. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
20. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
22. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
23. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
24. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-26 09:32:59.945 [ERRO] {5871fdebd64e8817ed40686bfdaf3d98} [tenants/internal/service.(*Tenant).DisableTenant] tenant_service.go:357: Query failed ... 
Stack:
1.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:357
2.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
3.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-26 09:38:37.583 [ERRO] {d8cf9027294f8817ef40686b132fef24} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:167: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:167
2.  tenants/internal/service.(*Tenant).updateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:243
3.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:360
4.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
5.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
7.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
10. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
12. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
14. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
16. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
18. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
20. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
22. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
23. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
24. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-26 09:38:37.585 [ERRO] {d8cf9027294f8817ef40686b132fef24} [tenants/internal/service.(*Tenant).DisableTenant] tenant_service.go:362: Query failed ... 
Stack:
1.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:362
2.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
3.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-26 09:40:54.612 [ERRO] {e0776a2c404f8817f040686b083a188f} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:167: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:167
2.  tenants/internal/service.(*Tenant).updateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:243
3.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:361
4.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
5.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
7.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
10. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
12. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
14. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
16. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
18. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
20. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
22. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
23. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
24. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-26 09:40:54.613 [ERRO] {e0776a2c404f8817f040686b083a188f} [tenants/internal/service.(*Tenant).DisableTenant] tenant_service.go:363: Query failed ... 
Stack:
1.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:363
2.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
3.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-26 17:24:27.372 [ERRO] {8039cc4b99688817da5b7e26491b8b06} [tenants/internal/service.(*Tenant).deleteTenant] tenant_service.go:233: status code: 405, error: {"code":405,"message":"method DELETE is not allowed, but [GET,POST] are"}
Stack:
1.  tenants/internal/service.(*Tenant).deleteTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:233
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:377
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-26 17:24:27.373 [ERRO] {8039cc4b99688817da5b7e26491b8b06} [tenants/internal/service.(*Tenant).DisableTenant] tenant_service.go:383: System error: status code: 405, error: {"code":405,"message":"method DELETE is not allowed, but [GET,POST] are"}
Stack:
1.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:383
2.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
3.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-26 17:25:47.584 [ERRO] {2858bf4fa4688817db5b7e268aa9881c} [tenants/internal/service.(*Tenant).deleteTenant] tenant_service.go:233: status code: 405, error: {"code":405,"message":"method DELETE is not allowed, but [GET,POST] are"}
Stack:
1.  tenants/internal/service.(*Tenant).deleteTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:233
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:377
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-26 17:25:47.586 [ERRO] {2858bf4fa4688817db5b7e268aa9881c} [tenants/internal/service.(*Tenant).DisableTenant] tenant_service.go:383: System error: status code: 405, error: {"code":405,"message":"method DELETE is not allowed, but [GET,POST] are"}
Stack:
1.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:383
2.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
3.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-26 17:26:33.985 [ERRO] {d89ad3bcad688817dc5b7e264099bb13} [tenants/internal/service.(*Tenant).deleteTenant] tenant_service.go:233: status code: -1, error: check the DerivedFromError field for more information: Delete "http://localhost:8070/v1/objects": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).deleteTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:233
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:377
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-26 17:26:33.987 [ERRO] {d89ad3bcad688817dc5b7e264099bb13} [tenants/internal/service.(*Tenant).DisableTenant] tenant_service.go:383: System error: status code: -1, error: check the DerivedFromError field for more information: Delete "http://localhost:8070/v1/objects": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:383
2.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
3.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

