2023-10-23 11:42:16.721 [ERRO] {70f46b06969f9017bf21034b1c778ff4} [tenants/internal/service.(*Tenant).createTenant] tenant_service.go:222: status code: 500, error: {"error":[{"message":"update vector: send POST request: Post \"https://qbibotopenai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2022-12-01\": net/http: TLS handshake timeout"}]}

Stack:
1.  tenants/internal/service.(*Tenant).createTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:222
2.  tenants/internal/service.(*Tenant).CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:333
3.  tenants/internal/service.CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:26
4.  tenants/internal/controller/tenants.(*ControllerV1).NewTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:58
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-23 11:42:16.724 [ERRO] {70f46b06969f9017bf21034b1c778ff4} [tenants/internal/service.(*Tenant).CreateTenant] tenant_service.go:335: System error: status code: 500, error: {"error":[{"message":"update vector: send POST request: Post \"https://qbibotopenai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2022-12-01\": net/http: TLS handshake timeout"}]}

Stack:
1.  tenants/internal/service.(*Tenant).CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:335
2.  tenants/internal/service.CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:26
3.  tenants/internal/controller/tenants.(*ControllerV1).NewTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:58
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
23. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
24. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
25. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
26. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
27. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
28. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-23 12:07:46.022 [ERRO] {985b3ab0fba09017c121034bc8adcc42} [tenants/internal/service.(*Tenant).createTenant] tenant_service.go:222: status code: 500, error: {"error":[{"message":"update vector: send POST request: Post \"https://qbibotopenai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2022-12-01\": EOF"}]}

Stack:
1.  tenants/internal/service.(*Tenant).createTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:222
2.  tenants/internal/service.(*Tenant).CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:345
3.  tenants/internal/service.CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:26
4.  tenants/internal/controller/tenants.(*ControllerV1).NewTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:58
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-23 12:07:46.038 [ERRO] {985b3ab0fba09017c121034bc8adcc42} [tenants/internal/service.(*Tenant).CreateTenant] tenant_service.go:347: System error: status code: 500, error: {"error":[{"message":"update vector: send POST request: Post \"https://qbibotopenai.openai.azure.com/openai/deployments/text-embedding-ada-002/embeddings?api-version=2022-12-01\": EOF"}]}

Stack:
1.  tenants/internal/service.(*Tenant).CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:347
2.  tenants/internal/service.CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:26
3.  tenants/internal/controller/tenants.(*ControllerV1).NewTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:58
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
23. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
24. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
25. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
26. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
27. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
28. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-23 12:19:33.404 [ERRO] {70c79c3ba1a19017c521034b49d155d9} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:507: status code: 404, error: 
Stack:
1.  tenants/internal/service.(*Tenant).ListTenants
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:507
2.  tenants/internal/service.ListTenants
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:37
3.  tenants/internal/controller/tenants.(*ControllerV1).ListTenants
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:86
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
23. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
24. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
25. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
26. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
27. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
28. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-23 17:07:44.219 [ERRO] {a888ec0f5bb190173c22034b50fe303b} [tenants/internal/service.(*Tenant).updateTenant] tenant_service.go:340: status code: 422, error: {"error":[{"message":"invalid object: no such prop with name 'app_key' found in class 'Tenants' in the schema. Check your schema files for which properties in this class are available"}]}

Stack:
1.  tenants/internal/service.(*Tenant).updateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:340
2.  tenants/internal/service.(*Tenant).UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:521
3.  tenants/internal/service.UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:35
4.  tenants/internal/controller/tenants.(*ControllerV1).UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:133
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-23 17:07:44.221 [ERRO] {a888ec0f5bb190173c22034b50fe303b} [tenants/internal/service.(*Tenant).UpdateTenant] tenant_service.go:525: System error
Stack:
1.  tenants/internal/service.(*Tenant).UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:525
2.  tenants/internal/service.UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:35
3.  tenants/internal/controller/tenants.(*ControllerV1).UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:133
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
23. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
24. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
25. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
26. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
27. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
28. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

