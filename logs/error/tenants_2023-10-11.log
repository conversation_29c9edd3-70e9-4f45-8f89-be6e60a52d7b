2023-10-11 12:00:50.141 [ERRO] {9054fde2a4f18c178d0fd854318c1fe2} [tenants/internal/service.(*Tenant).GenNewAppKeyForTenant] tenant_service.go:541: Enter  wrong password
Stack:
1.  tenants/internal/service.(*Tenant).GenNewAppKeyForTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:541
2.  tenants/internal/service.GenNewAppKey
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:41
3.  tenants/internal/controller/tenants.(*ControllerV1).GenNewKey
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:191
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
23. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
24. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
25. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
26. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
27. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
28. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-11 13:28:41.828 [ERRO] {d84a43196df68c17940fd854efabb164} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:170: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:170
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:362
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-11 13:29:49.423 [ERRO] {406543ce7bf68c17960fd854df2378f0} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:170: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:170
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:364
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-11 13:33:35.911 [ERRO] {90b460b9b0f68c17970fd8549c8138e9} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:170: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:170
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:362
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-11 13:36:15.536 [ERRO] {4896958cd2f68c17980fd854f0c5a442} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:170: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:170
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:362
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-11 13:39:47.324 [ERRO] {78235c46f4f68c179a0fd8543b7a7d30} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:170: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:170
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:362
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-11 13:48:35.926 [ERRO] {b804030437f78c179b0fd8546dc3c368} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:170: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:170
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:362
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-11 13:50:35.986 [ERRO] {e8121d9787f78c179c0fd854b7e04949} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:170: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:170
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:362
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-11 13:52:17.604 [ERRO] {78161571a4f78c179d0fd8545b6d33c2} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:170: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:170
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:362
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-11 13:55:17.488 [ERRO] {4817c835bbf78c179e0fd854a0cd4eb8} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:170: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:170
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:362
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-11 14:02:50.152 [ERRO] {70ddf49733f88c17a00fd85472dcc8db} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:170: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:170
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:362
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-11 14:03:12.800 [ERRO] {a0a484c24ff88c17a10fd8545fb53571} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:170: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:170
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:362
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-10-11 14:17:07.094 [ERRO] {18fa32c256f88c17a20fd85419641a8d} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:170: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:170
2.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:364
3.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
4.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.MiddlewareHandlerResponse
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_handler_response.go:25
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
24. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
25. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
26. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
27. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
28. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
29. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

