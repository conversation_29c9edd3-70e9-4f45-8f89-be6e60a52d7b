2023-09-25 16:00:06.896 [ERRO] {c8d9b09f5d158817d032ff073c17596e} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:156: Cannot query field "Tenant_Id" on type "Tenants". Did you mean "tenant_Id"?
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:156
2.  tenants/internal/service.(*Tenant).AuthTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:491
3.  tenants/internal/service.AuthTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:44
4.  tenants/internal/controller/tenants.(*ControllerV1).AuthTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:218
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 16:17:55.736 [ERRO] {10e383aa5f168817ccf1eb6dfa832b79} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:156: Cannot query field "Tenant_Id" on type "Tenants". Did you mean "tenant_Id"?
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:156
2.  tenants/internal/service.(*Tenant).CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:293
3.  tenants/internal/service.CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:26
4.  tenants/internal/controller/tenants.(*ControllerV1).NewTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:58
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 16:17:55.737 [ERRO] {10e383aa5f168817ccf1eb6dfa832b79} [tenants/internal/service.(*Tenant).CreateTenant] tenant_service.go:295: Cannot query field "Tenant_Id" on type "Tenants". Did you mean "tenant_Id"?
Stack:
1.  tenants/internal/service.(*Tenant).CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:295
2.  tenants/internal/service.CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:26
3.  tenants/internal/controller/tenants.(*ControllerV1).NewTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:58
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 16:21:44.215 [ERRO] {b872f10f96168817af5d104687d352eb} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:156: Cannot query field "Tenant_Id" on type "Tenants". Did you mean "tenant_Id"?
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:156
2.  tenants/internal/service.(*Tenant).CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:293
3.  tenants/internal/service.CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:26
4.  tenants/internal/controller/tenants.(*ControllerV1).NewTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:58
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 16:21:44.216 [ERRO] {b872f10f96168817af5d104687d352eb} [tenants/internal/service.(*Tenant).CreateTenant] tenant_service.go:295: Cannot query field "Tenant_Id" on type "Tenants". Did you mean "tenant_Id"?
Stack:
1.  tenants/internal/service.(*Tenant).CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:295
2.  tenants/internal/service.CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:26
3.  tenants/internal/controller/tenants.(*ControllerV1).NewTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:58
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 16:25:59.765 [ERRO] {3813413cb8168817ba9059444472709c} [tenants/internal/service.(*Tenant).createTenant] tenant_service.go:200: status code: 500, error: {"error":[{"message":"update vector: API Key: no api key found neither in request header: X-Openai-Api-Key nor in environment variable under OPENAI_APIKEY"}]}

Stack:
1.  tenants/internal/service.(*Tenant).createTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:200
2.  tenants/internal/service.(*Tenant).CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:300
3.  tenants/internal/service.CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:26
4.  tenants/internal/controller/tenants.(*ControllerV1).NewTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:58
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 16:27:52.491 [ERRO] {3813413cb8168817ba9059444472709c} [tenants/internal/service.(*Tenant).CreateTenant] tenant_service.go:302: System error: status code: 500, error: {"error":[{"message":"update vector: API Key: no api key found neither in request header: X-Openai-Api-Key nor in environment variable under OPENAI_APIKEY"}]}

Stack:
1.  tenants/internal/service.(*Tenant).CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:302
2.  tenants/internal/service.CreateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:26
3.  tenants/internal/controller/tenants.(*ControllerV1).NewTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:58
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 16:52:33.916 [ERRO] {08c47d8b4518881787ce960c4b1ea1ad} [tenants/internal/service.(*Tenant).updateTenant] tenant_service.go:250: status code: 422, error: {"error":[{"message":"invalid object: invalid uuid property 'app_Key' on class 'Tenants': invalid UUID format"}]}

Stack:
1.  tenants/internal/service.(*Tenant).updateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:250
2.  tenants/internal/service.(*Tenant).GenNewAppKeyForTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:482
3.  tenants/internal/service.GenNewAppKey
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:41
4.  tenants/internal/controller/tenants.(*ControllerV1).GenNewKey
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:191
5.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
6.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
9.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
10. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
11. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
12. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
13. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
14. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
15. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
16. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
17. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
18. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
19. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
20. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
21. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
22. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
23. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 17:15:25.850 [ERRO] {1853624d451988178ece960caf79b29a} [tenants/internal/service.(*Tenant).ListTenants] tenant_service.go:430: status code: -1, error: check the DerivedFromError field for more information: Get "http://localhost:8070/v1/objects?after=fd3f5bf6-761a-41e5-aca0-a38a8d4f9b1f&class=Tenants&limit=20": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).ListTenants
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:430
2.  tenants/internal/service.ListTenants
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:37
3.  tenants/internal/controller/tenants.(*ControllerV1).ListTenants
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:86
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 17:48:58.745 [ERRO] {1080bc484b1b8817d61a987198888e48} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:159: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:159
2.  tenants/internal/service.(*Tenant).updateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:234
3.  tenants/internal/service.(*Tenant).UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:368
4.  tenants/internal/service.UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:33
5.  tenants/internal/controller/tenants.(*ControllerV1).UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:133
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
7.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
10. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
12. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
14. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
16. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
18. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
20. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
22. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
23. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
24. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 17:48:58.746 [ERRO] {1080bc484b1b8817d61a987198888e48} [tenants/internal/service.(*Tenant).UpdateTenant] tenant_service.go:371: Query failed ... 
Stack:
1.  tenants/internal/service.(*Tenant).UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:371
2.  tenants/internal/service.UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:33
3.  tenants/internal/controller/tenants.(*ControllerV1).UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:133
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 17:59:35.224 [ERRO] {88cc50e5e11b8817d71a9871cd2ea944} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:160: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:160
2.  tenants/internal/service.(*Tenant).updateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:235
3.  tenants/internal/service.(*Tenant).UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:376
4.  tenants/internal/service.UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:33
5.  tenants/internal/controller/tenants.(*ControllerV1).UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:133
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
7.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
10. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
12. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
14. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
16. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
18. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
20. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
22. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
23. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
24. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 17:59:35.225 [ERRO] {88cc50e5e11b8817d71a9871cd2ea944} [tenants/internal/service.(*Tenant).UpdateTenant] tenant_service.go:379: Query failed ... 
Stack:
1.  tenants/internal/service.(*Tenant).UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:379
2.  tenants/internal/service.UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:33
3.  tenants/internal/controller/tenants.(*ControllerV1).UpdateTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:133
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 18:16:10.432 [ERRO] {20d7a76bc01c8817df1a9871eaeb1c09} [tenants/internal/service.(*Tenant).tenantIsExisted] tenant_service.go:160: status code: -1, error: check the DerivedFromError field for more information: status code: -1, error: check the DerivedFromError field for more information: Post "http://localhost:8070/v1/graphql": context canceled
Stack:
1.  tenants/internal/service.(*Tenant).tenantIsExisted
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:160
2.  tenants/internal/service.(*Tenant).updateTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:235
3.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:343
4.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
5.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
7.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
8.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
10. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
12. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
14. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
16. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
18. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
20. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
22. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
23. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
24. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

2023-09-25 18:16:10.433 [ERRO] {20d7a76bc01c8817df1a9871eaeb1c09} [tenants/internal/service.(*Tenant).DisableTenant] tenant_service.go:345: Query failed ... 
Stack:
1.  tenants/internal/service.(*Tenant).DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:345
2.  tenants/internal/service.DisableTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:29
3.  tenants/internal/controller/tenants.(*ControllerV1).RemoveTenant
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:159
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

