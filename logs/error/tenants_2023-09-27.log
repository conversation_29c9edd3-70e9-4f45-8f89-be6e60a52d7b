2023-09-27 15:12:44.109 [ERRO] {58437fbffdaf8817abf0623d3f9fd364} [tenants/internal/service.(*Tenant).GenNewAppKeyForTenant] tenant_service.go:541: Enter  wrong password
Stack:
1.  tenants/internal/service.(*Tenant).GenNewAppKeyForTenant
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant_service.go:541
2.  tenants/internal/service.GenNewAppKey
    /Users/<USER>/Sources/AI/tenants/internal/service/tenant.go:41
3.  tenants/internal/controller/tenants.(*ControllerV1).GenNewKey
    /Users/<USER>/Sources/AI/tenants/internal/controller/tenants/tenants_v1_teanants.go:191
4.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:153
5.  github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
6.  github.com/gogf/gf/v2/net/ghttp.(*middleware).callHandlerFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:130
7.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:76
8.  github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
9.  github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
10. tenants/internal/cmd.MiddleHandler
    /Users/<USER>/Sources/AI/tenants/internal/cmd/cmd.go:36
11. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:56
12. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
13. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:55
14. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
15. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
16. github.com/gogf/gf/v2/net/ghttp.internalMiddlewareServerTracing
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_middleware_tracing.go:79
17. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1.5
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:97
18. github.com/gogf/gf/v2/net/ghttp.niceCallFunc
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_func.go:61
19. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next.func1
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:96
20. github.com/gogf/gf/v2/util/gutil.TryCatch
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/util/gutil/gutil.go:57
21. github.com/gogf/gf/v2/net/ghttp.(*middleware).Next
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_request_middleware.go:50
22. github.com/gogf/gf/v2/net/ghttp.(*Server).ServeHTTP
    /Users/<USER>/go/pkg/mod/github.com/gogf/gf/v2@v2.5.2/net/ghttp/ghttp_server_handler.go:132

