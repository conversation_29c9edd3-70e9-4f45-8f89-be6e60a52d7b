2025-06-12T15:45:00.277+0800	WARN	config_client/config_client.go:335	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T15:45:00.277+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T15:45:00.277+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T15:45:00.277+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-7db7ec1e-d651-4035-bd80-bfcf23dffab3)
2025-06-12T15:45:00.277+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T15:45:00.277+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T15:45:00.277+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T15:45:00.277+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-7db7ec1e-d651-4035-bd80-bfcf23dffab3 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T15:45:00.277+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T15:45:00.278+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T15:45:00.278+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T15:45:00.382+0800	INFO	rpc/rpc_client.go:337	config-0-7db7ec1e-d651-4035-bd80-bfcf23dffab3 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749714300279_192.168.215.1_55260
2025-06-12T15:45:00.382+0800	INFO	rpc/rpc_client.go:486	config-0-7db7ec1e-d651-4035-bd80-bfcf23dffab3 notify connected event to listeners , connectionId=1749714300279_192.168.215.1_55260
2025-06-12T15:45:00.382+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T15:45:00.386+0800	ERROR	cache/disk_cache.go:105	make dir failed, dir path ./nacos/cfg/config, err: mkdir /Users/<USER>/Source/AI/tenants/./nacos/cfg/config: file exists.
2025-06-12T15:46:00.320+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T15:46:00.321+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T15:46:00.321+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-1b3e3aa4-9260-4cd4-bc57-2d37f71a8bb3)
2025-06-12T15:46:00.321+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T15:46:00.321+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T15:46:00.321+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T15:46:00.321+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-1b3e3aa4-9260-4cd4-bc57-2d37f71a8bb3 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T15:46:00.321+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T15:46:00.321+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T15:46:00.321+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T15:46:00.424+0800	INFO	rpc/rpc_client.go:337	config-0-1b3e3aa4-9260-4cd4-bc57-2d37f71a8bb3 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749714360322_192.168.215.1_56497
2025-06-12T15:46:00.424+0800	INFO	rpc/rpc_client.go:486	config-0-1b3e3aa4-9260-4cd4-bc57-2d37f71a8bb3 notify connected event to listeners , connectionId=1749714360322_192.168.215.1_56497
2025-06-12T15:46:00.424+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T16:04:30.220+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T16:04:30.220+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T16:04:30.220+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-13631ad7-73f8-43b4-8571-cbfece78faa9)
2025-06-12T16:04:30.220+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T16:04:30.220+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T16:04:30.220+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T16:04:30.220+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-13631ad7-73f8-43b4-8571-cbfece78faa9 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T16:04:30.220+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T16:04:30.220+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T16:04:30.220+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T16:04:30.323+0800	INFO	rpc/rpc_client.go:337	config-0-13631ad7-73f8-43b4-8571-cbfece78faa9 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749715470222_192.168.215.1_18585
2025-06-12T16:04:30.323+0800	INFO	rpc/rpc_client.go:486	config-0-13631ad7-73f8-43b4-8571-cbfece78faa9 notify connected event to listeners , connectionId=1749715470222_192.168.215.1_18585
2025-06-12T16:04:30.323+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T16:58:45.311+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T16:58:45.311+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T16:58:45.311+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-0741a3ce-ed52-4681-b8e0-b526d039040c)
2025-06-12T16:58:45.311+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T16:58:45.311+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T16:58:45.311+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T16:58:45.311+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-0741a3ce-ed52-4681-b8e0-b526d039040c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T16:58:45.311+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T16:58:45.311+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T16:58:45.311+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T16:58:45.413+0800	INFO	rpc/rpc_client.go:337	config-0-0741a3ce-ed52-4681-b8e0-b526d039040c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749718725312_192.168.215.1_51438
2025-06-12T16:58:45.414+0800	INFO	rpc/rpc_client.go:486	config-0-0741a3ce-ed52-4681-b8e0-b526d039040c notify connected event to listeners , connectionId=1749718725312_192.168.215.1_51438
2025-06-12T16:58:45.414+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T16:58:45.980+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T16:58:49.771+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T16:58:49.771+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-0c57a8b9-ec75-4c4a-ac9c-bab0eaf5a413)
2025-06-12T16:58:49.771+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T16:58:49.771+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T16:58:49.771+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T16:58:49.771+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-0c57a8b9-ec75-4c4a-ac9c-bab0eaf5a413 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T16:58:49.772+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T16:58:49.772+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T16:58:49.772+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T16:58:49.876+0800	INFO	rpc/rpc_client.go:337	config-0-0c57a8b9-ec75-4c4a-ac9c-bab0eaf5a413 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749718729774_192.168.215.1_19505
2025-06-12T16:58:49.876+0800	INFO	rpc/rpc_client.go:486	config-0-0c57a8b9-ec75-4c4a-ac9c-bab0eaf5a413 notify connected event to listeners , connectionId=1749718729774_192.168.215.1_19505
2025-06-12T16:58:49.876+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T17:05:21.098+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:05:21.098+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:05:21.098+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-c9226c63-9035-48eb-8d79-db5e9523b70a)
2025-06-12T17:05:21.098+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:05:21.098+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:05:21.098+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:05:21.098+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-c9226c63-9035-48eb-8d79-db5e9523b70a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:05:21.098+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:05:21.099+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:05:21.099+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:05:21.201+0800	INFO	rpc/rpc_client.go:337	config-0-c9226c63-9035-48eb-8d79-db5e9523b70a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749719121099_192.168.215.1_34094
2025-06-12T17:05:21.201+0800	INFO	rpc/rpc_client.go:486	config-0-c9226c63-9035-48eb-8d79-db5e9523b70a notify connected event to listeners , connectionId=1749719121099_192.168.215.1_34094
2025-06-12T17:05:21.201+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T17:05:52.005+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:05:52.005+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:05:52.005+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-5674680a-06c7-4e43-8d72-38e3d4642672)
2025-06-12T17:05:52.005+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:05:52.005+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:05:52.005+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:05:52.005+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-5674680a-06c7-4e43-8d72-38e3d4642672 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:05:52.005+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:05:52.005+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:05:52.005+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:05:52.108+0800	INFO	rpc/rpc_client.go:337	config-0-5674680a-06c7-4e43-8d72-38e3d4642672 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749719152006_192.168.215.1_17909
2025-06-12T17:05:52.108+0800	INFO	rpc/rpc_client.go:486	config-0-5674680a-06c7-4e43-8d72-38e3d4642672 notify connected event to listeners , connectionId=1749719152006_192.168.215.1_17909
2025-06-12T17:05:52.108+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T17:06:32.748+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:06:32.748+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:06:32.748+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-2000138a-37b5-43d7-aaff-e0759c819dac)
2025-06-12T17:06:32.748+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:06:32.748+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:06:32.748+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:06:32.748+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-2000138a-37b5-43d7-aaff-e0759c819dac try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:06:32.748+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:06:32.748+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:06:32.748+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:06:32.851+0800	INFO	rpc/rpc_client.go:337	config-0-2000138a-37b5-43d7-aaff-e0759c819dac success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749719192749_192.168.215.1_57811
2025-06-12T17:06:32.851+0800	INFO	rpc/rpc_client.go:486	config-0-2000138a-37b5-43d7-aaff-e0759c819dac notify connected event to listeners , connectionId=1749719192749_192.168.215.1_57811
2025-06-12T17:06:32.851+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T17:06:36.041+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:06:36.041+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:06:36.041+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-2e70a890-a2b1-4be2-abec-5f597334834b)
2025-06-12T17:06:36.041+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:06:36.041+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:06:36.041+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:06:36.041+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-2e70a890-a2b1-4be2-abec-5f597334834b try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:06:36.041+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:06:36.041+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:06:36.041+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:06:36.144+0800	INFO	rpc/rpc_client.go:337	config-0-2e70a890-a2b1-4be2-abec-5f597334834b success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749719196043_192.168.215.1_61112
2025-06-12T17:06:36.144+0800	INFO	rpc/rpc_client.go:486	config-0-2e70a890-a2b1-4be2-abec-5f597334834b notify connected event to listeners , connectionId=1749719196043_192.168.215.1_61112
2025-06-12T17:06:36.144+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T17:08:55.589+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:08:55.589+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:08:55.589+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-caf3587f-0d61-4443-9206-de2dd983a2c3)
2025-06-12T17:08:55.589+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:08:55.589+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:08:55.589+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:08:55.589+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-caf3587f-0d61-4443-9206-de2dd983a2c3 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:08:55.589+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:08:55.589+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:08:55.589+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:08:55.692+0800	INFO	rpc/rpc_client.go:337	config-0-caf3587f-0d61-4443-9206-de2dd983a2c3 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749719335591_192.168.215.1_50628
2025-06-12T17:08:55.693+0800	INFO	rpc/rpc_client.go:486	config-0-caf3587f-0d61-4443-9206-de2dd983a2c3 notify connected event to listeners , connectionId=1749719335591_192.168.215.1_50628
2025-06-12T17:08:55.693+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T17:42:03.166+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:42:03.166+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:42:03.166+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-8a06abc5-d6c8-4936-aa3e-f1afd08b5bbb)
2025-06-12T17:42:03.166+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:42:03.166+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:42:03.166+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:42:03.166+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-8a06abc5-d6c8-4936-aa3e-f1afd08b5bbb try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:42:03.166+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:42:03.166+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:42:03.166+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:42:03.269+0800	INFO	rpc/rpc_client.go:337	config-0-8a06abc5-d6c8-4936-aa3e-f1afd08b5bbb success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749721323167_192.168.215.1_46866
2025-06-12T17:42:03.269+0800	INFO	rpc/rpc_client.go:486	config-0-8a06abc5-d6c8-4936-aa3e-f1afd08b5bbb notify connected event to listeners , connectionId=1749721323167_192.168.215.1_46866
2025-06-12T17:42:03.269+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T17:42:03.273+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T17:42:03.279+0800	WARN	naming_cache/service_info_holder.go:81	instance list is empty, updateCacheWhenEmpty is set to false, callback is not triggered. service name:dsh.svc
2025-06-12T17:42:03.327+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T17:42:03.329+0800	WARN	naming_cache/service_info_holder.go:81	instance list is empty, updateCacheWhenEmpty is set to false, callback is not triggered. service name:dsh.svc
2025-06-12T17:42:58.493+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:42:58.493+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:42:58.493+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-a7bd00dd-373d-45df-9191-dd3a84e0dc24)
2025-06-12T17:42:58.493+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:42:58.493+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:42:58.493+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:42:58.493+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-a7bd00dd-373d-45df-9191-dd3a84e0dc24 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:42:58.493+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:42:58.493+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:42:58.493+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:42:58.596+0800	INFO	rpc/rpc_client.go:337	config-0-a7bd00dd-373d-45df-9191-dd3a84e0dc24 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749721378495_192.168.215.1_52497
2025-06-12T17:42:58.597+0800	INFO	rpc/rpc_client.go:486	config-0-a7bd00dd-373d-45df-9191-dd3a84e0dc24 notify connected event to listeners , connectionId=1749721378495_192.168.215.1_52497
2025-06-12T17:42:58.597+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T17:42:58.602+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T17:42:58.604+0800	WARN	naming_cache/service_info_holder.go:81	instance list is empty, updateCacheWhenEmpty is set to false, callback is not triggered. service name:dsh.svc
2025-06-12T17:42:58.632+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T17:42:58.633+0800	WARN	naming_cache/service_info_holder.go:81	instance list is empty, updateCacheWhenEmpty is set to false, callback is not triggered. service name:dsh.svc
2025-06-12T17:43:05.702+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:43:05.702+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:43:05.702+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-b335e68e-3790-4154-b019-c5e2428f190d)
2025-06-12T17:43:05.702+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:43:05.702+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:43:05.702+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:43:05.702+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-b335e68e-3790-4154-b019-c5e2428f190d try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:43:05.702+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:43:05.702+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:43:05.702+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:43:05.806+0800	INFO	rpc/rpc_client.go:337	config-0-b335e68e-3790-4154-b019-c5e2428f190d success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749721385703_192.168.215.1_19670
2025-06-12T17:43:05.806+0800	INFO	rpc/rpc_client.go:486	config-0-b335e68e-3790-4154-b019-c5e2428f190d notify connected event to listeners , connectionId=1749721385703_192.168.215.1_19670
2025-06-12T17:43:05.806+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T17:43:05.812+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T17:43:05.813+0800	WARN	naming_cache/service_info_holder.go:81	instance list is empty, updateCacheWhenEmpty is set to false, callback is not triggered. service name:dsh.svc
2025-06-12T17:43:05.838+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T17:43:05.840+0800	WARN	naming_cache/service_info_holder.go:81	instance list is empty, updateCacheWhenEmpty is set to false, callback is not triggered. service name:dsh.svc
2025-06-12T17:46:13.101+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:46:13.101+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:46:13.101+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-45306569-9d08-4aea-acd7-5628dc27e2a8)
2025-06-12T17:46:13.101+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:46:13.101+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:46:13.101+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:46:13.101+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-45306569-9d08-4aea-acd7-5628dc27e2a8 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:46:13.101+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:46:13.101+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:46:13.101+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:46:13.204+0800	INFO	rpc/rpc_client.go:337	config-0-45306569-9d08-4aea-acd7-5628dc27e2a8 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749721573102_192.168.215.1_33453
2025-06-12T17:46:13.205+0800	INFO	rpc/rpc_client.go:486	config-0-45306569-9d08-4aea-acd7-5628dc27e2a8 notify connected event to listeners , connectionId=1749721573102_192.168.215.1_33453
2025-06-12T17:46:13.205+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T17:46:13.211+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T17:46:13.214+0800	INFO	naming_cache/service_info_holder.go:96	service key:DEFAULT_GROUP@@dsh.svc@@DEFAULT was updated to:{"cacheMillis":10000,"hosts":[{"instanceId":"*************#8087#DEFAULT#DEFAULT_GROUP@@dsh.svc","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dsh.svc","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*************#8087#DEFAULT#DEFAULT_GROUP@@dsh.svc","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dsh.svc","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000},{"instanceId":"************#8087#DEFAULT#DEFAULT_GROUP@@dsh.svc","ip":"************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dsh.svc","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000},{"instanceId":"*************#8087#DEFAULT#DEFAULT_GROUP@@dsh.svc","ip":"*************","port":8087,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"DEFAULT_GROUP@@dsh.svc","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":5000,"ipDeleteTimeout":30000,"instanceHeartBeatTimeOut":15000}],"checksum":"","lastRefTime":1749721573211,"clusters":"DEFAULT","name":"dsh.svc","groupName":"DEFAULT_GROUP","valid":true,"allIPs":false,"reachProtectionThreshold":false}
2025-06-12T17:46:13.215+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T17:46:13.215+0800	WARN	naming_cache/service_info_holder.go:89	out of date data received, old-t: 1749721573211, new-t: 1749721573211
2025-06-12T17:58:21.601+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T17:58:21.602+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T17:58:21.602+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-1de9dcb5-afe1-4977-8a85-eef219b476ce)
2025-06-12T17:58:21.602+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:58:21.602+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:58:21.602+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:58:21.602+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-1de9dcb5-afe1-4977-8a85-eef219b476ce try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:58:21.602+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:58:21.602+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:58:21.602+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:58:21.704+0800	INFO	rpc/rpc_client.go:337	config-0-1de9dcb5-afe1-4977-8a85-eef219b476ce success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722301614_192.168.215.1_17980
2025-06-12T17:58:21.705+0800	INFO	rpc/rpc_client.go:486	config-0-1de9dcb5-afe1-4977-8a85-eef219b476ce notify connected event to listeners , connectionId=1749722301614_192.168.215.1_17980
2025-06-12T17:58:21.705+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T17:58:21.712+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T17:58:21.712+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:02:54.619+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:02:54.619+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:02:54.619+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:02:54.619+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:02:54.619+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9925cd35-cdfa-4962-b9bd-bb89fb5bcf37)
2025-06-12T18:02:54.619+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-732656ec-2298-4b17-9b3b-7bca73973478)
2025-06-12T18:02:54.619+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:02:54.619+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:02:54.619+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:02:54.619+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:02:54.619+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:02:54.619+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:02:54.619+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9925cd35-cdfa-4962-b9bd-bb89fb5bcf37 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:02:54.619+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-732656ec-2298-4b17-9b3b-7bca73973478 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:02:54.619+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:02:54.619+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:02:54.619+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:02:54.619+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:02:54.619+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:02:54.619+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:02:54.722+0800	INFO	rpc/rpc_client.go:337	config-0-732656ec-2298-4b17-9b3b-7bca73973478 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722574635_192.168.215.1_61950
2025-06-12T18:02:54.722+0800	INFO	rpc/rpc_client.go:337	config-0-9925cd35-cdfa-4962-b9bd-bb89fb5bcf37 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722574635_192.168.215.1_44288
2025-06-12T18:02:54.723+0800	INFO	rpc/rpc_client.go:486	config-0-9925cd35-cdfa-4962-b9bd-bb89fb5bcf37 notify connected event to listeners , connectionId=1749722574635_192.168.215.1_44288
2025-06-12T18:02:54.723+0800	INFO	rpc/rpc_client.go:486	config-0-732656ec-2298-4b17-9b3b-7bca73973478 notify connected event to listeners , connectionId=1749722574635_192.168.215.1_61950
2025-06-12T18:02:54.723+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:02:54.723+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:02:54.728+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:02:54.728+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:02:54.729+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:02:54.729+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:04:46.196+0800	INFO	rpc/rpc_client.go:486	9bb00a6b-3f9b-40ef-ae7e-3d531bf0dc8f notify connected event to listeners , connectionId=1749722686094_192.168.215.1_17620
2025-06-12T18:04:46.259+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:04:46.259+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:04:46.259+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:04:46.259+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:04:46.259+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-003c831d-2c34-47fe-b9b2-ed84cfe839ab)
2025-06-12T18:04:46.259+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-77eb3f63-8033-4125-85d8-f294e459beef)
2025-06-12T18:04:46.259+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:04:46.259+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:04:46.259+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:04:46.259+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:04:46.259+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:04:46.259+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-003c831d-2c34-47fe-b9b2-ed84cfe839ab try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:04:46.259+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:04:46.259+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:04:46.259+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:04:46.259+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-77eb3f63-8033-4125-85d8-f294e459beef try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:04:46.259+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:04:46.259+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:04:46.259+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:04:46.259+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:04:46.363+0800	INFO	rpc/rpc_client.go:337	config-0-77eb3f63-8033-4125-85d8-f294e459beef success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722686260_192.168.215.1_30467
2025-06-12T18:04:46.363+0800	INFO	rpc/rpc_client.go:337	config-0-003c831d-2c34-47fe-b9b2-ed84cfe839ab success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722686260_192.168.215.1_58360
2025-06-12T18:04:46.363+0800	INFO	rpc/rpc_client.go:486	config-0-77eb3f63-8033-4125-85d8-f294e459beef notify connected event to listeners , connectionId=1749722686260_192.168.215.1_30467
2025-06-12T18:04:46.363+0800	INFO	rpc/rpc_client.go:486	config-0-003c831d-2c34-47fe-b9b2-ed84cfe839ab notify connected event to listeners , connectionId=1749722686260_192.168.215.1_58360
2025-06-12T18:04:46.363+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:04:46.363+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:04:46.367+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:04:46.367+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:04:46.367+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:04:46.367+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:06:09.605+0800	INFO	rpc/rpc_client.go:486	32f93af9-ab24-4000-ac7f-5a3fba5a1554 notify connected event to listeners , connectionId=1749722769501_192.168.215.1_36924
2025-06-12T18:06:09.670+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:06:09.670+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:06:09.670+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-cde8f323-de71-4fd3-a548-26cf38c734b1)
2025-06-12T18:06:09.670+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:06:09.670+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:06:09.670+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:06:09.671+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-cde8f323-de71-4fd3-a548-26cf38c734b1 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:06:09.671+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:06:09.671+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:06:09.671+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:06:09.773+0800	INFO	rpc/rpc_client.go:337	config-0-cde8f323-de71-4fd3-a548-26cf38c734b1 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722769672_192.168.215.1_20149
2025-06-12T18:06:09.774+0800	INFO	rpc/rpc_client.go:486	config-0-cde8f323-de71-4fd3-a548-26cf38c734b1 notify connected event to listeners , connectionId=1749722769672_192.168.215.1_20149
2025-06-12T18:06:09.774+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:06:09.780+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:06:09.780+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:06:49.978+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:06:49.978+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:06:49.978+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9fda3747-c72b-4f29-87d0-a470d9df3b76)
2025-06-12T18:06:49.978+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:06:49.978+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:06:49.978+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:06:49.978+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9fda3747-c72b-4f29-87d0-a470d9df3b76 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:06:49.978+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:06:49.978+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:06:49.978+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:06:50.081+0800	INFO	rpc/rpc_client.go:337	config-0-9fda3747-c72b-4f29-87d0-a470d9df3b76 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722809979_192.168.215.1_47619
2025-06-12T18:06:50.081+0800	INFO	rpc/rpc_client.go:486	config-0-9fda3747-c72b-4f29-87d0-a470d9df3b76 notify connected event to listeners , connectionId=1749722809979_192.168.215.1_47619
2025-06-12T18:06:50.081+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:06:50.088+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:06:50.088+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:10:29.607+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:10:29.607+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:10:29.607+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-40bbfc40-e33d-45a5-aafd-bb5bb4c00777)
2025-06-12T18:10:29.607+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:10:29.607+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:10:29.607+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:10:29.607+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-40bbfc40-e33d-45a5-aafd-bb5bb4c00777 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:10:29.607+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:10:29.608+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:10:29.608+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:10:29.608+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:10:29.609+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:10:29.609+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-fce6a769-43b0-489e-b983-7f2489fbace0)
2025-06-12T18:10:29.609+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:10:29.609+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:10:29.609+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:10:29.609+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-fce6a769-43b0-489e-b983-7f2489fbace0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:10:29.609+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:10:29.609+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:10:29.609+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:10:29.709+0800	INFO	rpc/rpc_client.go:337	config-0-40bbfc40-e33d-45a5-aafd-bb5bb4c00777 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723029608_192.168.215.1_17962
2025-06-12T18:10:29.710+0800	INFO	rpc/rpc_client.go:486	config-0-40bbfc40-e33d-45a5-aafd-bb5bb4c00777 notify connected event to listeners , connectionId=1749723029608_192.168.215.1_17962
2025-06-12T18:10:29.710+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:10:29.710+0800	INFO	rpc/rpc_client.go:337	config-0-fce6a769-43b0-489e-b983-7f2489fbace0 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723029609_192.168.215.1_60512
2025-06-12T18:10:29.710+0800	INFO	rpc/rpc_client.go:486	config-0-fce6a769-43b0-489e-b983-7f2489fbace0 notify connected event to listeners , connectionId=1749723029609_192.168.215.1_60512
2025-06-12T18:10:29.710+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:10:29.715+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:10:29.715+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:10:29.715+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:10:29.715+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:10:41.024+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:10:41.025+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:10:41.025+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-b2bf4379-26c2-4aab-adf7-c070e7ccf833)
2025-06-12T18:10:41.025+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:10:41.025+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:10:41.025+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:10:41.025+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-b2bf4379-26c2-4aab-adf7-c070e7ccf833 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:10:41.025+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:10:41.025+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:10:41.025+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:10:41.129+0800	INFO	rpc/rpc_client.go:337	config-0-b2bf4379-26c2-4aab-adf7-c070e7ccf833 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723041026_192.168.215.1_60392
2025-06-12T18:10:41.129+0800	INFO	rpc/rpc_client.go:486	config-0-b2bf4379-26c2-4aab-adf7-c070e7ccf833 notify connected event to listeners , connectionId=1749723041026_192.168.215.1_60392
2025-06-12T18:10:41.130+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:10:41.136+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:10:41.136+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:11:42.369+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:11:42.370+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:11:42.370+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-37d270fd-e1b9-4b4c-a224-be56e4805d4c)
2025-06-12T18:11:42.371+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:11:42.371+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:11:42.374+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:11:42.374+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-37d270fd-e1b9-4b4c-a224-be56e4805d4c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:11:42.375+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:11:42.375+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:11:42.375+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:11:42.481+0800	INFO	rpc/rpc_client.go:337	config-0-37d270fd-e1b9-4b4c-a224-be56e4805d4c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723102378_192.168.215.1_20978
2025-06-12T18:11:42.482+0800	INFO	rpc/rpc_client.go:486	config-0-37d270fd-e1b9-4b4c-a224-be56e4805d4c notify connected event to listeners , connectionId=1749723102378_192.168.215.1_20978
2025-06-12T18:11:42.482+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:11:42.488+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:11:42.488+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:16:51.806+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:16:51.807+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:16:51.807+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-3bae0ccf-0d8f-44bb-99b7-03a598e40b90)
2025-06-12T18:16:51.807+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:16:51.807+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:16:51.807+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:16:51.807+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-3bae0ccf-0d8f-44bb-99b7-03a598e40b90 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:16:51.807+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:16:51.807+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:16:51.807+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:16:51.910+0800	INFO	rpc/rpc_client.go:337	config-0-3bae0ccf-0d8f-44bb-99b7-03a598e40b90 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723411808_192.168.215.1_59109
2025-06-12T18:16:51.910+0800	INFO	rpc/rpc_client.go:486	config-0-3bae0ccf-0d8f-44bb-99b7-03a598e40b90 notify connected event to listeners , connectionId=1749723411808_192.168.215.1_59109
2025-06-12T18:16:51.910+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:16:51.917+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:16:51.917+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:16:51.918+0800	WARN	naming_cache/service_info_holder.go:81	instance list is empty, updateCacheWhenEmpty is set to false, callback is not triggered. service name:dsh.svc
2025-06-12T18:16:51.930+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<tenants.svc> with instance:<[{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T18:16:52.501+0800	WARN	naming_cache/service_info_holder.go:81	instance list is empty, updateCacheWhenEmpty is set to false, callback is not triggered. service name:dsh.svc
2025-06-12T18:22:53.236+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:22:53.237+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:22:53.237+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-86a5cd27-c9be-4434-b5cc-08319d450bf1)
2025-06-12T18:22:53.237+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:22:53.237+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:22:53.237+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:22:53.237+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-86a5cd27-c9be-4434-b5cc-08319d450bf1 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:22:53.237+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:22:53.237+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:22:53.237+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:22:53.340+0800	INFO	rpc/rpc_client.go:337	config-0-86a5cd27-c9be-4434-b5cc-08319d450bf1 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723773214_192.168.215.1_39245
2025-06-12T18:22:53.340+0800	INFO	rpc/rpc_client.go:486	config-0-86a5cd27-c9be-4434-b5cc-08319d450bf1 notify connected event to listeners , connectionId=1749723773214_192.168.215.1_39245
2025-06-12T18:22:53.340+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:22:53.344+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:22:53.344+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:22:53.355+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<tenants.svc> with instance:<[{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T18:28:10.087+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:28:10.087+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:28:10.088+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:28:10.088+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-ec3f331e-6451-484c-b73b-ddca5e61d74c)
2025-06-12T18:28:10.088+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:28:10.088+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:28:10.088+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:28:10.088+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:28:10.088+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-c91f47c9-658d-4f1a-ab9f-98ad0fb48148)
2025-06-12T18:28:10.088+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:28:10.088+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-ec3f331e-6451-484c-b73b-ddca5e61d74c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:28:10.088+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:28:10.088+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:28:10.088+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:28:10.088+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:28:10.088+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-c91f47c9-658d-4f1a-ab9f-98ad0fb48148 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:28:10.088+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:28:10.088+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:28:10.088+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:28:10.088+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:28:10.192+0800	INFO	rpc/rpc_client.go:337	config-0-ec3f331e-6451-484c-b73b-ddca5e61d74c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724090090_192.168.215.1_57843
2025-06-12T18:28:10.192+0800	INFO	rpc/rpc_client.go:337	config-0-c91f47c9-658d-4f1a-ab9f-98ad0fb48148 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724090090_192.168.215.1_63473
2025-06-12T18:28:10.192+0800	INFO	rpc/rpc_client.go:486	config-0-c91f47c9-658d-4f1a-ab9f-98ad0fb48148 notify connected event to listeners , connectionId=1749724090090_192.168.215.1_63473
2025-06-12T18:28:10.192+0800	INFO	rpc/rpc_client.go:486	config-0-ec3f331e-6451-484c-b73b-ddca5e61d74c notify connected event to listeners , connectionId=1749724090090_192.168.215.1_57843
2025-06-12T18:28:10.192+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:28:10.192+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:28:10.197+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:28:10.197+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:28:10.197+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:28:10.197+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:28:10.214+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<tenants.svc> with instance:<[{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T18:28:54.232+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:28:54.232+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:28:54.232+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-c67d3f86-f1e2-4745-bfe9-d6c8e1d9be41)
2025-06-12T18:28:54.232+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:28:54.232+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:28:54.232+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:28:54.232+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-c67d3f86-f1e2-4745-bfe9-d6c8e1d9be41 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:28:54.232+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:28:54.232+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:28:54.232+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:28:54.233+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:28:54.233+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:28:54.233+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-9e6b6d9e-47df-4bcc-8d14-a656ec9408c7)
2025-06-12T18:28:54.233+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:28:54.233+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:28:54.233+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:28:54.233+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-9e6b6d9e-47df-4bcc-8d14-a656ec9408c7 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:28:54.233+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:28:54.233+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:28:54.233+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:28:54.336+0800	INFO	rpc/rpc_client.go:337	config-0-9e6b6d9e-47df-4bcc-8d14-a656ec9408c7 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724134237_192.168.215.1_35022
2025-06-12T18:28:54.336+0800	INFO	rpc/rpc_client.go:337	config-0-c67d3f86-f1e2-4745-bfe9-d6c8e1d9be41 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724134235_192.168.215.1_61636
2025-06-12T18:28:54.337+0800	INFO	rpc/rpc_client.go:486	config-0-9e6b6d9e-47df-4bcc-8d14-a656ec9408c7 notify connected event to listeners , connectionId=1749724134237_192.168.215.1_35022
2025-06-12T18:28:54.337+0800	INFO	rpc/rpc_client.go:486	config-0-c67d3f86-f1e2-4745-bfe9-d6c8e1d9be41 notify connected event to listeners , connectionId=1749724134235_192.168.215.1_61636
2025-06-12T18:28:54.337+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:28:54.337+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:28:54.343+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:28:54.343+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:28:54.343+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:28:54.343+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:28:54.355+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<tenants.svc> with instance:<[{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T18:31:50.167+0800	INFO	rpc/rpc_client.go:486	ddfaeb84-285d-41c8-b890-d7740e6cc415 notify connected event to listeners , connectionId=1749724310068_192.168.215.1_20765
2025-06-12T18:31:50.233+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:31:50.233+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:31:50.234+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-5a7c0558-4e35-46f8-832b-ee49d279ee96)
2025-06-12T18:31:50.234+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:31:50.234+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:31:50.234+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:31:50.234+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-5a7c0558-4e35-46f8-832b-ee49d279ee96 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:31:50.234+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:31:50.234+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:31:50.234+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:31:50.234+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:31:50.234+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:31:50.234+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-84d81a35-fc1d-4f51-84ef-7dfb9277ff4e)
2025-06-12T18:31:50.234+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:31:50.234+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:31:50.234+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:31:50.234+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-84d81a35-fc1d-4f51-84ef-7dfb9277ff4e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:31:50.234+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:31:50.234+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:31:50.234+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:31:50.336+0800	INFO	rpc/rpc_client.go:337	config-0-5a7c0558-4e35-46f8-832b-ee49d279ee96 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724310239_192.168.215.1_60965
2025-06-12T18:31:50.336+0800	INFO	rpc/rpc_client.go:337	config-0-84d81a35-fc1d-4f51-84ef-7dfb9277ff4e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724310239_192.168.215.1_59824
2025-06-12T18:31:50.336+0800	INFO	rpc/rpc_client.go:486	config-0-5a7c0558-4e35-46f8-832b-ee49d279ee96 notify connected event to listeners , connectionId=1749724310239_192.168.215.1_60965
2025-06-12T18:31:50.336+0800	INFO	rpc/rpc_client.go:486	config-0-84d81a35-fc1d-4f51-84ef-7dfb9277ff4e notify connected event to listeners , connectionId=1749724310239_192.168.215.1_59824
2025-06-12T18:31:50.337+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:31:50.337+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:31:50.342+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:31:50.342+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:31:50.342+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:31:50.342+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:31:50.356+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<tenants.svc> with instance:<[{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
2025-06-12T18:34:04.112+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:34:04.112+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:34:04.112+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-37fbf8f4-65f3-4321-9cf2-8c74ed84a27e)
2025-06-12T18:34:04.112+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:34:04.112+0800	WARN	cache/disk_cache.go:155	read cache file Config Encrypted Data Key failed. cause file doesn't exist, file path: ./nacos/cfg/config/encrypted-data-key/tenants.yaml@@DEFAULT_GROUP@@dev.: file not exist
2025-06-12T18:34:04.112+0800	WARN	cache/disk_cache.go:193	read Config Content failed. cause file doesn't exist, file path: ./nacos/cfg/config/tenants.yaml@@DEFAULT_GROUP@@dev_failover.
2025-06-12T18:34:04.112+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:34:04.112+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:34:04.112+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=config-0-86dabc79-f095-4fa3-bc98-09130929ada5)
2025-06-12T18:34:04.112+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:34:04.112+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:34:04.112+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-37fbf8f4-65f3-4321-9cf2-8c74ed84a27e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:34:04.112+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:34:04.112+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:34:04.112+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:34:04.112+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] config-0-86dabc79-f095-4fa3-bc98-09130929ada5 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:34:04.112+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:34:04.112+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:34:04.112+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:34:04.112+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:34:04.215+0800	INFO	rpc/rpc_client.go:337	config-0-37fbf8f4-65f3-4321-9cf2-8c74ed84a27e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724444113_192.168.215.1_27465
2025-06-12T18:34:04.215+0800	INFO	rpc/rpc_client.go:337	config-0-86dabc79-f095-4fa3-bc98-09130929ada5 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724444113_192.168.215.1_54526
2025-06-12T18:34:04.215+0800	INFO	rpc/rpc_client.go:486	config-0-86dabc79-f095-4fa3-bc98-09130929ada5 notify connected event to listeners , connectionId=1749724444113_192.168.215.1_54526
2025-06-12T18:34:04.215+0800	INFO	rpc/rpc_client.go:486	config-0-37fbf8f4-65f3-4321-9cf2-8c74ed84a27e notify connected event to listeners , connectionId=1749724444113_192.168.215.1_27465
2025-06-12T18:34:04.215+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:34:04.215+0800	INFO	config_client/config_connection_event_listener.go:38	[ConfigConnectionEventListener] connect to config server for taskId: 0
2025-06-12T18:34:04.220+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:34:04.220+0800	INFO	naming_client/naming_client.go:277	select instances with options: [healthy:<true>], with service:<DEFAULT_GROUP@@dsh.svc>
2025-06-12T18:34:04.220+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:34:04.220+0800	INFO	naming_grpc/naming_grpc_proxy.go:177	Subscribe Service namespaceId:<dev>, serviceName:<dsh.svc>, groupName:<DEFAULT_GROUP>, clusters:<DEFAULT>
2025-06-12T18:34:04.232+0800	INFO	naming_grpc/naming_grpc_proxy.go:108	batch register instance namespaceId:<dev>,serviceName:<tenants.svc> with instance:<[{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0},{"instanceId":"","ip":"*************","port":8084,"weight":100,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"DEFAULT","serviceName":"","metadata":{"insecure":"true","protocol":"http"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}]>
