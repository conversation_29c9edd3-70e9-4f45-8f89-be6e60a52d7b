server:
 address: ":8084"
 swaggerPath: "/swagger"
 openapiPath: "/api.json" 
logger:
  path: "./logs"
  file:  "tenants_{Y-m-d}.log"
  rotateSize: "10M"
  rotateBackupLimit: 3 
  rotateBackupExpire: "3d"
  rotateBackupCompress: 9
  level: "dev"
  stdout: true
weaviate:
 host: localhost:8070
 scheme: http
 recreate_class_onstart: false
 reimport_data_onstart: false
 query_page_size: 100 
system:
 access_token_expire: "1d"
 refresh_token_expire: "7d"
 issuer: "Ai3"
 secret_key: "seckey"
 admin_account: "admin"
 admin_pass: "a~1d@3m$"
 update_services_duration: "1m"   
 # for data sync hub service
 data_service:
   name: dsh.svc
   scheme: http  
rabbitMQ:
  url: "amqp://admin:admin@127.0.0.1:5672/"
   
services:
  -  service_name: "Ai20240513001"
     key: "13ug1b5287ph9lfe8495cx87vep8m4986ii32n0718d2h1j07d"
     include:
      - micro_service: "quizto"
        actions: ["chat"]
  -  service_name: "Ai20250303002"
     key: "556q80w7356n8lgil98s7pg1mx506041fu563alc1yi6j1a6sz"
     include:
      - micro_service: "quizto"
        actions: ["chat"]
  -  service_name: "Gpt20240108001"
     key: "rgz5854n4n3152i783ns07dw953f1sx76j602bk2dj34bw2158"
     include:
       -  micro_service: "quizto"
          actions: ["chat"]
  -  service_name: "Q202404"
     key: "5bf9e576-26ab-4f19-839f-9d4ad4380576"
     include:
       -  micro_service: "quizto"
          actions: ["chat"]
  -  service_name: "Ai20240830001"
     key: "0hwas05c73tq9w0uag614867x15q43tod0qz6jk26lup48563j"
     include:
       -  micro_service: "quizto"
          actions: ["chat"]
 
permissions:
  exclude:
   quizto: ["deleteall","get_tenants"]
redis:
  default:
    address: 127.0.0.1:6379
    db: 0
    

llms:
 - llm_name: "QbiBotOpenAI"
   description: "AOAI of Qbibot"
   base_url: "https://qbibotopenai.openai.azure.com/"
   token: "050928bf5c1b47e5b3a2da43b164f383"
   resource_name: "qbibotopenai"
   embedding_model: "text-embedding-ada-002"
   model: "gpt-35-turbo"
   api_version: "2024-03-01-preview"
   temperature: 0.0
   max_token: 3000 
   llm_type: aoai

 - llm_name: "AileChatBot-Gpt4"
   description: "AOAI Gpt-4 of aile "
   base_url: "https://aile-chatbot-sn.openai.azure.com/"
   token: "********************************"
   resource_name: "aile-chatbot-sn"
   embedding_model: "text-embedding-ada-002"
   model: "gpt-4"
   api_version: "2024-03-01-preview"
   temperature: 0.0
   max_token: 2000
   llm_type: aoai

 - llm_name: "AileChatBot-Gpt4o"
   description: "AOAI Gpt-4o of aile "
   base_url: "https://aile-chatbot.openai.azure.com/"
   token: "********************************"
   resource_name: "aile-chatbot"
   embedding_model: "text-embedding-ada-002"
   model: "gpt-4o"
   api_version: "2024-05-01-preview"
   temperature: 0.0
   max_token: 2000
   llm_type: aoai
   
 - llm_name: "AileChatBot"
   description: "Claude of Aile chat"
   base_url: "https://aile-chatbot.openai.azure.com/"
   token: {"token":"********************************", "aws_access_key_id":"********************","aws_secret_access_key":"0x/yfENJJXTfflnZyaFmuXuHzm727buoqLGcfmrv"}
   resource_name: "us-east-1"
   embedding_model: "text-embedding-ada-002"
   #model: "anthropic.claude-3-sonnet-20240229-v1:0"
   model: "anthropic.claude-3-5-sonnet-20240620-v1:0"
   api_version: "bedrock-2023-05-31"
   temperature: 0.0
   max_token: 4096
   llm_type: ClaudeAI      
  #  description: "AOAI of Aile"
  #  base_url: "https://cs-aoai-lab.openai.azure.com/"
  #  token: "df944e6008d64f0e974d12036e1c26da"
  #  resource_name: "cs-aoai-lab"
  #  embedding_model: "text-embedding-ada-002"
  #  model: "gpt-4-v"
  #  api_version: "2024-03-01-preview"
  #  temperature: 0.0
  #  max_token: 4096
  #  llm_type: aoai 
llm:
  check_llms_interval: "10s"
  token: "********************************"
  resourceName: "aile-chatbot"
  deploymentId: "text-embedding-ada-002"



