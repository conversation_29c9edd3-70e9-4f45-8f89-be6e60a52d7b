2025-06-12T15:45:00.002+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T15:45:00.106+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55310
2025-06-12T15:45:00.106+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=5c45cdd6-ee4f-4255-8af3-63ea9583eaa6)
2025-06-12T15:45:00.106+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T15:45:00.106+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T15:45:00.107+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T15:45:00.107+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 5c45cdd6-ee4f-4255-8af3-63ea9583eaa6 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T15:45:00.107+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T15:45:00.107+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T15:45:00.107+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T15:45:00.108+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T15:45:00.211+0800	INFO	rpc/rpc_client.go:337	5c45cdd6-ee4f-4255-8af3-63ea9583eaa6 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749714300109_192.168.215.1_56890
2025-06-12T15:45:00.211+0800	INFO	rpc/rpc_client.go:486	5c45cdd6-ee4f-4255-8af3-63ea9583eaa6 notify connected event to listeners , connectionId=1749714300109_192.168.215.1_56890
2025-06-12T15:46:00.050+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T15:46:00.148+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55869
2025-06-12T15:46:00.148+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=e98891d2-143f-4015-840c-a4b759abca42)
2025-06-12T15:46:00.148+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T15:46:00.148+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T15:46:00.148+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T15:46:00.148+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] e98891d2-143f-4015-840c-a4b759abca42 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T15:46:00.148+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T15:46:00.148+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T15:46:00.148+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T15:46:00.149+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T15:46:00.251+0800	INFO	rpc/rpc_client.go:337	e98891d2-143f-4015-840c-a4b759abca42 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749714360150_192.168.215.1_45774
2025-06-12T15:46:00.252+0800	INFO	rpc/rpc_client.go:486	e98891d2-143f-4015-840c-a4b759abca42 notify connected event to listeners , connectionId=1749714360150_192.168.215.1_45774
2025-06-12T16:04:29.949+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T16:04:30.052+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55384
2025-06-12T16:04:30.052+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ce6c5e39-dca9-4b22-b44a-e7dd90e32245)
2025-06-12T16:04:30.053+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T16:04:30.053+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T16:04:30.053+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T16:04:30.053+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ce6c5e39-dca9-4b22-b44a-e7dd90e32245 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T16:04:30.053+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T16:04:30.053+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T16:04:30.053+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T16:04:30.054+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T16:04:30.156+0800	INFO	rpc/rpc_client.go:337	ce6c5e39-dca9-4b22-b44a-e7dd90e32245 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749715470055_192.168.215.1_53609
2025-06-12T16:04:30.157+0800	INFO	rpc/rpc_client.go:486	ce6c5e39-dca9-4b22-b44a-e7dd90e32245 notify connected event to listeners , connectionId=1749715470055_192.168.215.1_53609
2025-06-12T16:58:45.059+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T16:58:45.144+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55021
2025-06-12T16:58:45.144+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=174cf3e5-c470-4fa5-8dd0-ef3a000f5f88)
2025-06-12T16:58:45.144+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T16:58:45.144+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T16:58:45.144+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T16:58:45.144+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 174cf3e5-c470-4fa5-8dd0-ef3a000f5f88 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T16:58:45.144+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T16:58:45.144+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T16:58:45.144+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T16:58:45.144+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T16:58:45.247+0800	INFO	rpc/rpc_client.go:337	174cf3e5-c470-4fa5-8dd0-ef3a000f5f88 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749718725145_192.168.215.1_33959
2025-06-12T16:58:45.248+0800	INFO	rpc/rpc_client.go:486	174cf3e5-c470-4fa5-8dd0-ef3a000f5f88 notify connected event to listeners , connectionId=1749718725145_192.168.215.1_33959
2025-06-12T16:58:45.742+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T16:58:45.820+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55821
2025-06-12T16:58:45.820+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=63125c91-d136-40c3-a78b-82b759dec70d)
2025-06-12T16:58:45.820+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T16:58:45.820+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T16:58:45.820+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T16:58:45.820+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 63125c91-d136-40c3-a78b-82b759dec70d try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T16:58:45.820+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T16:58:45.820+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T16:58:45.820+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T16:58:45.821+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T16:58:45.923+0800	INFO	rpc/rpc_client.go:337	63125c91-d136-40c3-a78b-82b759dec70d success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749718725821_192.168.215.1_20279
2025-06-12T16:58:45.923+0800	INFO	rpc/rpc_client.go:486	63125c91-d136-40c3-a78b-82b759dec70d notify connected event to listeners , connectionId=1749718725821_192.168.215.1_20279
2025-06-12T17:05:20.838+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T17:05:20.923+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55029
2025-06-12T17:05:20.923+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=0850cc96-0a1c-4b3c-a66e-1ce5fb18b35b)
2025-06-12T17:05:20.923+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:05:20.923+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:05:20.923+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:05:20.923+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 0850cc96-0a1c-4b3c-a66e-1ce5fb18b35b try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:05:20.923+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:05:20.923+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:05:20.923+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:05:20.923+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:05:21.025+0800	INFO	rpc/rpc_client.go:337	0850cc96-0a1c-4b3c-a66e-1ce5fb18b35b success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749719120924_192.168.215.1_57587
2025-06-12T17:05:21.026+0800	INFO	rpc/rpc_client.go:486	0850cc96-0a1c-4b3c-a66e-1ce5fb18b35b notify connected event to listeners , connectionId=1749719120924_192.168.215.1_57587
2025-06-12T17:05:51.729+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T17:05:51.830+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55118
2025-06-12T17:05:51.830+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=f487b819-4f40-4df1-8275-ab856487e514)
2025-06-12T17:05:51.830+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:05:51.830+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:05:51.830+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:05:51.830+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] f487b819-4f40-4df1-8275-ab856487e514 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:05:51.830+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:05:51.830+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:05:51.830+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:05:51.830+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:05:51.933+0800	INFO	rpc/rpc_client.go:337	f487b819-4f40-4df1-8275-ab856487e514 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749719151831_192.168.215.1_32204
2025-06-12T17:05:51.933+0800	INFO	rpc/rpc_client.go:486	f487b819-4f40-4df1-8275-ab856487e514 notify connected event to listeners , connectionId=1749719151831_192.168.215.1_32204
2025-06-12T17:06:32.474+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T17:06:32.576+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55304
2025-06-12T17:06:32.576+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=98ed2755-e152-46a1-b08f-7b014e6d8090)
2025-06-12T17:06:32.577+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:06:32.577+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:06:32.577+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:06:32.577+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 98ed2755-e152-46a1-b08f-7b014e6d8090 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:06:32.577+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:06:32.577+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:06:32.577+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:06:32.577+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:06:32.681+0800	INFO	rpc/rpc_client.go:337	98ed2755-e152-46a1-b08f-7b014e6d8090 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749719192579_192.168.215.1_43670
2025-06-12T17:06:32.681+0800	INFO	rpc/rpc_client.go:486	98ed2755-e152-46a1-b08f-7b014e6d8090 notify connected event to listeners , connectionId=1749719192579_192.168.215.1_43670
2025-06-12T17:06:35.768+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T17:06:35.867+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55217
2025-06-12T17:06:35.868+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=68efc8da-d939-42ab-ab62-4447bae58788)
2025-06-12T17:06:35.868+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:06:35.868+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:06:35.868+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:06:35.868+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 68efc8da-d939-42ab-ab62-4447bae58788 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:06:35.868+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:06:35.868+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:06:35.868+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:06:35.869+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:06:35.972+0800	INFO	rpc/rpc_client.go:337	68efc8da-d939-42ab-ab62-4447bae58788 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749719195869_192.168.215.1_22315
2025-06-12T17:06:35.972+0800	INFO	rpc/rpc_client.go:486	68efc8da-d939-42ab-ab62-4447bae58788 notify connected event to listeners , connectionId=1749719195869_192.168.215.1_22315
2025-06-12T17:08:55.311+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T17:08:55.413+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55285
2025-06-12T17:08:55.413+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=af8d9961-3966-4a19-9f0d-8873f05596d3)
2025-06-12T17:08:55.413+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:08:55.413+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:08:55.413+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:08:55.413+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] af8d9961-3966-4a19-9f0d-8873f05596d3 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:08:55.413+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:08:55.413+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:08:55.413+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:08:55.414+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:08:55.517+0800	INFO	rpc/rpc_client.go:337	af8d9961-3966-4a19-9f0d-8873f05596d3 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749719335415_192.168.215.1_17474
2025-06-12T17:08:55.518+0800	INFO	rpc/rpc_client.go:486	af8d9961-3966-4a19-9f0d-8873f05596d3 notify connected event to listeners , connectionId=1749719335415_192.168.215.1_17474
2025-06-12T17:42:02.903+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T17:42:02.983+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55357
2025-06-12T17:42:02.984+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=6026a937-6466-44c6-879f-ec290c3c1c06)
2025-06-12T17:42:02.984+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:42:02.984+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:42:02.984+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:42:02.984+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 6026a937-6466-44c6-879f-ec290c3c1c06 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:42:02.984+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:42:02.984+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:42:02.984+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:42:02.984+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:42:03.087+0800	INFO	rpc/rpc_client.go:337	6026a937-6466-44c6-879f-ec290c3c1c06 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749721322985_192.168.215.1_23044
2025-06-12T17:42:03.087+0800	INFO	rpc/rpc_client.go:486	6026a937-6466-44c6-879f-ec290c3c1c06 notify connected event to listeners , connectionId=1749721322985_192.168.215.1_23044
2025-06-12T17:42:58.219+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T17:42:58.319+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55648
2025-06-12T17:42:58.319+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ce67b3cb-b82e-4867-873b-d81e33ebf6a1)
2025-06-12T17:42:58.319+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:42:58.319+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:42:58.319+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:42:58.319+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ce67b3cb-b82e-4867-873b-d81e33ebf6a1 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:42:58.319+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:42:58.319+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:42:58.319+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:42:58.319+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:42:58.422+0800	INFO	rpc/rpc_client.go:337	ce67b3cb-b82e-4867-873b-d81e33ebf6a1 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749721378320_192.168.215.1_23128
2025-06-12T17:42:58.422+0800	INFO	rpc/rpc_client.go:486	ce67b3cb-b82e-4867-873b-d81e33ebf6a1 notify connected event to listeners , connectionId=1749721378320_192.168.215.1_23128
2025-06-12T17:43:05.431+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T17:43:05.531+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55797
2025-06-12T17:43:05.531+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=afa4cab1-205e-4b31-ade1-45092f646c17)
2025-06-12T17:43:05.531+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:43:05.531+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:43:05.531+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:43:05.531+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] afa4cab1-205e-4b31-ade1-45092f646c17 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:43:05.531+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:43:05.531+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:43:05.531+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:43:05.532+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:43:05.634+0800	INFO	rpc/rpc_client.go:337	afa4cab1-205e-4b31-ade1-45092f646c17 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749721385533_192.168.215.1_21665
2025-06-12T17:43:05.635+0800	INFO	rpc/rpc_client.go:486	afa4cab1-205e-4b31-ade1-45092f646c17 notify connected event to listeners , connectionId=1749721385533_192.168.215.1_21665
2025-06-12T17:46:12.826+0800	ERROR	cache/disk_cache.go:75	read cacheDir:./nacos/reg/naming/dev failed!err:open ./nacos/reg/naming/dev: no such file or directory
2025-06-12T17:46:12.926+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55824
2025-06-12T17:46:12.927+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=3ea24331-2ed6-497c-887b-6801fa9bb93a)
2025-06-12T17:46:12.927+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:46:12.927+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:46:12.927+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:46:12.927+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 3ea24331-2ed6-497c-887b-6801fa9bb93a try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:46:12.927+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:46:12.927+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:46:12.927+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:46:12.928+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:46:13.031+0800	INFO	rpc/rpc_client.go:337	3ea24331-2ed6-497c-887b-6801fa9bb93a success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749721572929_192.168.215.1_48549
2025-06-12T17:46:13.031+0800	INFO	rpc/rpc_client.go:486	3ea24331-2ed6-497c-887b-6801fa9bb93a notify connected event to listeners , connectionId=1749721572929_192.168.215.1_48549
2025-06-12T17:58:21.335+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T17:58:21.430+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55235
2025-06-12T17:58:21.430+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=6f0d95cf-79b0-4f72-a7bc-342ad323d663)
2025-06-12T17:58:21.430+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T17:58:21.430+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T17:58:21.430+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T17:58:21.430+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 6f0d95cf-79b0-4f72-a7bc-342ad323d663 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T17:58:21.430+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T17:58:21.430+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T17:58:21.430+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T17:58:21.431+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T17:58:21.533+0800	INFO	rpc/rpc_client.go:337	6f0d95cf-79b0-4f72-a7bc-342ad323d663 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722301443_192.168.215.1_53669
2025-06-12T17:58:21.533+0800	INFO	rpc/rpc_client.go:486	6f0d95cf-79b0-4f72-a7bc-342ad323d663 notify connected event to listeners , connectionId=1749722301443_192.168.215.1_53669
2025-06-12T18:02:54.341+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:02:54.341+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:02:54.445+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55128
2025-06-12T18:02:54.445+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55019
2025-06-12T18:02:54.446+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=c3df7a69-ccf3-4889-8980-620aa05d9f23)
2025-06-12T18:02:54.446+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=2d949a8b-2070-4cc5-98bd-ca4afd0322d9)
2025-06-12T18:02:54.446+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:02:54.446+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:02:54.446+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:02:54.446+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:02:54.446+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:02:54.446+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:02:54.446+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] c3df7a69-ccf3-4889-8980-620aa05d9f23 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:02:54.446+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 2d949a8b-2070-4cc5-98bd-ca4afd0322d9 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:02:54.446+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:02:54.446+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:02:54.446+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:02:54.446+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:02:54.446+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:02:54.446+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:02:54.446+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:02:54.446+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:02:54.549+0800	INFO	rpc/rpc_client.go:337	c3df7a69-ccf3-4889-8980-620aa05d9f23 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722574462_192.168.215.1_37795
2025-06-12T18:02:54.549+0800	INFO	rpc/rpc_client.go:337	2d949a8b-2070-4cc5-98bd-ca4afd0322d9 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722574462_192.168.215.1_35453
2025-06-12T18:02:54.550+0800	INFO	rpc/rpc_client.go:486	c3df7a69-ccf3-4889-8980-620aa05d9f23 notify connected event to listeners , connectionId=1749722574462_192.168.215.1_37795
2025-06-12T18:02:54.552+0800	INFO	rpc/rpc_client.go:486	2d949a8b-2070-4cc5-98bd-ca4afd0322d9 notify connected event to listeners , connectionId=1749722574462_192.168.215.1_35453
2025-06-12T18:04:46.008+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:04:46.008+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:04:46.091+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55736
2025-06-12T18:04:46.091+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55154
2025-06-12T18:04:46.092+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=1f460634-4925-4c8c-89d3-ff94dcf438e1)
2025-06-12T18:04:46.092+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=9bb00a6b-3f9b-40ef-ae7e-3d531bf0dc8f)
2025-06-12T18:04:46.092+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:04:46.092+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:04:46.092+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:04:46.092+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:04:46.092+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:04:46.092+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:04:46.092+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 1f460634-4925-4c8c-89d3-ff94dcf438e1 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:04:46.092+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 9bb00a6b-3f9b-40ef-ae7e-3d531bf0dc8f try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:04:46.092+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:04:46.092+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:04:46.092+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:04:46.092+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:04:46.092+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:04:46.092+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:04:46.093+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:04:46.093+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:04:46.195+0800	INFO	rpc/rpc_client.go:337	1f460634-4925-4c8c-89d3-ff94dcf438e1 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722686094_192.168.215.1_42602
2025-06-12T18:04:46.195+0800	INFO	rpc/rpc_client.go:486	1f460634-4925-4c8c-89d3-ff94dcf438e1 notify connected event to listeners , connectionId=1749722686094_192.168.215.1_42602
2025-06-12T18:04:46.195+0800	INFO	rpc/rpc_client.go:337	9bb00a6b-3f9b-40ef-ae7e-3d531bf0dc8f success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722686094_192.168.215.1_17620
2025-06-12T18:06:09.400+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:06:09.500+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55809
2025-06-12T18:06:09.500+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=32f93af9-ab24-4000-ac7f-5a3fba5a1554)
2025-06-12T18:06:09.500+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:06:09.500+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:06:09.500+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:06:09.500+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 32f93af9-ab24-4000-ac7f-5a3fba5a1554 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:06:09.500+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:06:09.500+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:06:09.500+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:06:09.500+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:06:09.603+0800	INFO	rpc/rpc_client.go:337	32f93af9-ab24-4000-ac7f-5a3fba5a1554 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722769501_192.168.215.1_36924
2025-06-12T18:06:49.712+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:06:49.806+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55253
2025-06-12T18:06:49.806+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=a57b730f-a735-4a0a-be4e-a93aa61936ed)
2025-06-12T18:06:49.806+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:06:49.806+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:06:49.806+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:06:49.806+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] a57b730f-a735-4a0a-be4e-a93aa61936ed try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:06:49.806+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:06:49.806+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:06:49.806+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:06:49.807+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:06:49.909+0800	INFO	rpc/rpc_client.go:337	a57b730f-a735-4a0a-be4e-a93aa61936ed success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749722809808_192.168.215.1_59378
2025-06-12T18:06:49.910+0800	INFO	rpc/rpc_client.go:486	a57b730f-a735-4a0a-be4e-a93aa61936ed notify connected event to listeners , connectionId=1749722809808_192.168.215.1_59378
2025-06-12T18:10:29.341+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:10:29.341+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:10:29.439+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55275
2025-06-12T18:10:29.439+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=64c76166-49e8-4d1e-a936-df5f1593e58b)
2025-06-12T18:10:29.439+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:10:29.439+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:10:29.439+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:10:29.439+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 64c76166-49e8-4d1e-a936-df5f1593e58b try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:10:29.439+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:10:29.439+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:10:29.439+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:10:29.440+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:10:29.440+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55381
2025-06-12T18:10:29.441+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=d1c72a74-0690-4b75-a84a-9b8132d651ee)
2025-06-12T18:10:29.441+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:10:29.441+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:10:29.441+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:10:29.441+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] d1c72a74-0690-4b75-a84a-9b8132d651ee try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:10:29.441+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:10:29.441+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:10:29.441+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:10:29.441+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:10:29.543+0800	INFO	rpc/rpc_client.go:337	d1c72a74-0690-4b75-a84a-9b8132d651ee success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723029441_192.168.215.1_33563
2025-06-12T18:10:29.543+0800	INFO	rpc/rpc_client.go:337	64c76166-49e8-4d1e-a936-df5f1593e58b success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723029441_192.168.215.1_16631
2025-06-12T18:10:29.543+0800	INFO	rpc/rpc_client.go:486	64c76166-49e8-4d1e-a936-df5f1593e58b notify connected event to listeners , connectionId=1749723029441_192.168.215.1_16631
2025-06-12T18:10:29.543+0800	INFO	rpc/rpc_client.go:486	d1c72a74-0690-4b75-a84a-9b8132d651ee notify connected event to listeners , connectionId=1749723029441_192.168.215.1_33563
2025-06-12T18:10:40.754+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:10:40.853+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55435
2025-06-12T18:10:40.853+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=1a952a2e-2a62-4d3b-a1e7-f35656982add)
2025-06-12T18:10:40.853+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:10:40.853+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:10:40.853+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:10:40.853+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 1a952a2e-2a62-4d3b-a1e7-f35656982add try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:10:40.854+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:10:40.854+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:10:40.854+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:10:40.855+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:10:40.957+0800	INFO	rpc/rpc_client.go:337	1a952a2e-2a62-4d3b-a1e7-f35656982add success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723040856_192.168.215.1_29946
2025-06-12T18:10:40.957+0800	INFO	rpc/rpc_client.go:486	1a952a2e-2a62-4d3b-a1e7-f35656982add notify connected event to listeners , connectionId=1749723040856_192.168.215.1_29946
2025-06-12T18:11:42.098+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:11:42.196+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55910
2025-06-12T18:11:42.196+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=9a614e4b-8c7f-46da-8c0f-efa3d5bccb44)
2025-06-12T18:11:42.197+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:11:42.197+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:11:42.197+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:11:42.197+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 9a614e4b-8c7f-46da-8c0f-efa3d5bccb44 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:11:42.197+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:11:42.197+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:11:42.197+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:11:42.198+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:11:42.301+0800	INFO	rpc/rpc_client.go:337	9a614e4b-8c7f-46da-8c0f-efa3d5bccb44 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723102198_192.168.215.1_33165
2025-06-12T18:11:42.301+0800	INFO	rpc/rpc_client.go:486	9a614e4b-8c7f-46da-8c0f-efa3d5bccb44 notify connected event to listeners , connectionId=1749723102198_192.168.215.1_33165
2025-06-12T18:16:51.538+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:16:51.636+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55451
2025-06-12T18:16:51.636+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=a8069558-3ca7-4835-a487-4cfd47c4ebd6)
2025-06-12T18:16:51.636+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:16:51.636+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:16:51.636+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:16:51.636+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] a8069558-3ca7-4835-a487-4cfd47c4ebd6 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:16:51.636+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:16:51.636+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:16:51.636+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:16:51.637+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:16:51.739+0800	INFO	rpc/rpc_client.go:337	a8069558-3ca7-4835-a487-4cfd47c4ebd6 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723411638_192.168.215.1_24835
2025-06-12T18:16:51.739+0800	INFO	rpc/rpc_client.go:486	a8069558-3ca7-4835-a487-4cfd47c4ebd6 notify connected event to listeners , connectionId=1749723411638_192.168.215.1_24835
2025-06-12T18:22:52.964+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:22:53.062+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55840
2025-06-12T18:22:53.063+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=6349a41b-150c-435f-b87c-a8961516172b)
2025-06-12T18:22:53.063+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:22:53.063+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:22:53.063+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:22:53.063+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 6349a41b-150c-435f-b87c-a8961516172b try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:22:53.063+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:22:53.063+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:22:53.063+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:22:53.064+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:22:53.167+0800	INFO	rpc/rpc_client.go:337	6349a41b-150c-435f-b87c-a8961516172b success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749723773040_192.168.215.1_26805
2025-06-12T18:22:53.167+0800	INFO	rpc/rpc_client.go:486	6349a41b-150c-435f-b87c-a8961516172b notify connected event to listeners , connectionId=1749723773040_192.168.215.1_26805
2025-06-12T18:28:09.813+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:28:09.813+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:28:09.913+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55649
2025-06-12T18:28:09.913+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55329
2025-06-12T18:28:09.913+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=fa616fc3-312c-4c88-834c-6d5c50e2f393)
2025-06-12T18:28:09.913+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=b2827a9e-00c4-4a26-a39f-2426ee0c4ad0)
2025-06-12T18:28:09.913+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:28:09.913+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:28:09.913+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:28:09.913+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:28:09.913+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:28:09.913+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:28:09.913+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] fa616fc3-312c-4c88-834c-6d5c50e2f393 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:28:09.913+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] b2827a9e-00c4-4a26-a39f-2426ee0c4ad0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:28:09.913+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:28:09.913+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:28:09.913+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:28:09.913+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:28:09.914+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:28:09.914+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:28:09.914+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:28:09.914+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:28:10.017+0800	INFO	rpc/rpc_client.go:337	fa616fc3-312c-4c88-834c-6d5c50e2f393 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724089916_192.168.215.1_59452
2025-06-12T18:28:10.017+0800	INFO	rpc/rpc_client.go:337	b2827a9e-00c4-4a26-a39f-2426ee0c4ad0 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724089916_192.168.215.1_33944
2025-06-12T18:28:10.017+0800	INFO	rpc/rpc_client.go:486	fa616fc3-312c-4c88-834c-6d5c50e2f393 notify connected event to listeners , connectionId=1749724089916_192.168.215.1_59452
2025-06-12T18:28:10.018+0800	INFO	rpc/rpc_client.go:486	b2827a9e-00c4-4a26-a39f-2426ee0c4ad0 notify connected event to listeners , connectionId=1749724089916_192.168.215.1_33944
2025-06-12T18:28:53.962+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:28:53.961+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:28:54.058+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55191
2025-06-12T18:28:54.058+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55742
2025-06-12T18:28:54.058+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=f00afbb5-236e-4ff4-ae20-52a0e4f51b67)
2025-06-12T18:28:54.058+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=be27ea4a-4ac1-44e6-8d44-27df51ea1c7e)
2025-06-12T18:28:54.058+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:28:54.058+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:28:54.058+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:28:54.058+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:28:54.058+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:28:54.058+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:28:54.058+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] be27ea4a-4ac1-44e6-8d44-27df51ea1c7e try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:28:54.058+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] f00afbb5-236e-4ff4-ae20-52a0e4f51b67 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:28:54.058+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:28:54.058+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:28:54.058+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:28:54.058+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:28:54.058+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:28:54.058+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:28:54.058+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:28:54.058+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:28:54.162+0800	INFO	rpc/rpc_client.go:337	be27ea4a-4ac1-44e6-8d44-27df51ea1c7e success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724134061_192.168.215.1_43270
2025-06-12T18:28:54.162+0800	INFO	rpc/rpc_client.go:337	f00afbb5-236e-4ff4-ae20-52a0e4f51b67 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724134061_192.168.215.1_51894
2025-06-12T18:28:54.162+0800	INFO	rpc/rpc_client.go:486	be27ea4a-4ac1-44e6-8d44-27df51ea1c7e notify connected event to listeners , connectionId=1749724134061_192.168.215.1_43270
2025-06-12T18:28:54.163+0800	INFO	rpc/rpc_client.go:486	f00afbb5-236e-4ff4-ae20-52a0e4f51b67 notify connected event to listeners , connectionId=1749724134061_192.168.215.1_51894
2025-06-12T18:31:49.958+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:31:49.958+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:31:50.061+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55945
2025-06-12T18:31:50.061+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55636
2025-06-12T18:31:50.062+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=35b1e20a-3fc2-41f0-a073-c0689338656c)
2025-06-12T18:31:50.062+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ddfaeb84-285d-41c8-b890-d7740e6cc415)
2025-06-12T18:31:50.062+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:31:50.062+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:31:50.062+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:31:50.062+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:31:50.062+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:31:50.062+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:31:50.062+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ddfaeb84-285d-41c8-b890-d7740e6cc415 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:31:50.062+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 35b1e20a-3fc2-41f0-a073-c0689338656c try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:31:50.062+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:31:50.062+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:31:50.062+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:31:50.062+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:31:50.062+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:31:50.062+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:31:50.062+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:31:50.062+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:31:50.165+0800	INFO	rpc/rpc_client.go:337	35b1e20a-3fc2-41f0-a073-c0689338656c success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724310068_192.168.215.1_62340
2025-06-12T18:31:50.165+0800	INFO	rpc/rpc_client.go:337	ddfaeb84-285d-41c8-b890-d7740e6cc415 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724310068_192.168.215.1_20765
2025-06-12T18:31:50.166+0800	INFO	rpc/rpc_client.go:486	35b1e20a-3fc2-41f0-a073-c0689338656c notify connected event to listeners , connectionId=1749724310068_192.168.215.1_62340
2025-06-12T18:34:03.847+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:34:03.847+0800	INFO	cache/disk_cache.go:97	finish loading name cache, total: 1
2025-06-12T18:34:03.943+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55687
2025-06-12T18:34:03.943+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55400
2025-06-12T18:34:03.943+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=91a6964f-80b9-4cbc-aa1e-fdfefafa8ed0)
2025-06-12T18:34:03.943+0800	INFO	rpc/rpc_client.go:157	init rpc client for name %!(EXTRA string=ac65c2ae-e36c-499a-a357-e8fcc22a7f02)
2025-06-12T18:34:03.943+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:34:03.943+0800	INFO	rpc/rpc_client.go:166	get app conn labels from client config map[] 
2025-06-12T18:34:03.943+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:34:03.943+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:34:03.943+0800	INFO	rpc/rpc_client.go:168	get app conn labels from env map[] 
2025-06-12T18:34:03.943+0800	INFO	rpc/rpc_client.go:171	final app conn labels : map[] 
2025-06-12T18:34:03.943+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] 91a6964f-80b9-4cbc-aa1e-fdfefafa8ed0 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:34:03.943+0800	INFO	rpc/rpc_client.go:327	[RpcClient.Start] ac65c2ae-e36c-499a-a357-e8fcc22a7f02 try to connect to server on start up, server: {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848}
2025-06-12T18:34:03.943+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:34:03.943+0800	INFO	rpc/grpc_client.go:175	check tls config %!(EXTRA *constant.TLSConfig=&{false false false    })
2025-06-12T18:34:03.943+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:34:03.943+0800	INFO	rpc/grpc_client.go:180	try to get tls config from env
2025-06-12T18:34:03.943+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:34:03.943+0800	INFO	rpc/grpc_client.go:189	 tls config from env is not enable
2025-06-12T18:34:03.943+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:34:03.943+0800	INFO	util/common.go:96	Local IP:*************
2025-06-12T18:34:04.045+0800	INFO	rpc/rpc_client.go:337	91a6964f-80b9-4cbc-aa1e-fdfefafa8ed0 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724443944_192.168.215.1_52968
2025-06-12T18:34:04.045+0800	INFO	rpc/rpc_client.go:337	ac65c2ae-e36c-499a-a357-e8fcc22a7f02 success to connect to server {serverIp:127.0.0.1 serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1749724443944_192.168.215.1_27477
2025-06-12T18:34:04.045+0800	INFO	rpc/rpc_client.go:486	ac65c2ae-e36c-499a-a357-e8fcc22a7f02 notify connected event to listeners , connectionId=1749724443944_192.168.215.1_27477
