package tenants

import (
	"context"

	v1 "tenants/api/tenants/v1"
)

type ITenantsV1 interface {
	NewTenant(ctx context.Context, req *v1.NewTenantReq) (res *v1.NewTenantRes, err error)
	ListTenants(ctx context.Context, req *v1.ListTenantsReq) (res *v1.ListTenantsRes, err error)
	UpdateTenant(ctx context.Context, req *v1.UpdateTenantReq) (res *v1.UpdateTenantRes, err error)
	RemoveTenant(ctx context.Context, req *v1.RemoveTenantReq) (res *v1.RemoveTenantRes, err error)
	GenNewKey(ctx context.Context, req *v1.GenNewKeyReq) (res *v1.GenNewKeyRes, err error)
	AuthTenant(ctx context.Context, req *v1.AuthTenantReq) (res *v1.AuthTenantRes, err error)
	NewTokens(ctx context.Context, req *v1.NewTokensReq) (res *v1.NewTokensRes, err error)
	RefreshToken(ctx context.Context, req *v1.RefreshTokenReq) (res *v1.RefreshTokenRes, err error)
	GetLLMs(ctx context.Context, req *v1.GetLLMsReq) (res *v1.GetLLMsRes, err error)
	GetLLMByTenant(ctx context.Context, req *v1.GetLLMByTenantReq) (res *v1.GetLLMByTenantRes, err error)
	AssignLLMToTenant(ctx context.Context, req *v1.AssignLLMToTenantReq) (res *v1.AssignLLMToTenantRes, err error)
	RemoveTenantLLM(ctx context.Context, req *v1.RemoveTenantLLMReq) (res *v1.RemoveTenantLLMRes, err error)
	GetLLMTenantList(ctx context.Context, req *v1.GetLLMTenantListReq) (res *v1.GetLLMTenantListRes, err error)
	DeactiveTenant(ctx context.Context, req *v1.DeactivateTenantReq) (res *v1.DeactivateTenantRes, err error)
	ActiveTenant(ctx context.Context, req *v1.ActiveTenantReq) (res *v1.ActiveTenantRes, err error)
	Remark(ctx context.Context, req *v1.RemarkReq) (res *v1.RemarkRes, err error)
	SetTenantParams(ctx context.Context, req *v1.SetTenantParamsReq) (res *v1.SetTenantParamsRes, err error)
	GetTenantParams(ctx context.Context, req *v1.GetTenantParamsReq) (res *v1.GetTenantParamsRes, err error)
	GetSysInstruction(ctx context.Context, req *v1.GetSysInstructionReq) (res *v1.GetSysInstructionRes, err error)
	SetSysInstruction(ctx context.Context, req *v1.SetSysInstructionReq) (res *v1.SetSysInstructionRes, err error)
}
