package v1

import (
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/frame/g"
	"tenants/internal/model"
)

type SetTenantParamsReq struct {
	g.Meta   `path:"/v1/tenants/setTenantsParams" method:"post" tags:"Tenants" summary:"set parameters of tenant"`
	TenantID string      `json:"tenant_id" v:"required"`
	Params   *gjson.Json `json:"params"`
}
type SetTenantParamsRes struct {
	CodeMessage
}

type GetTenantParamsReq struct {
	g.Meta   `path:"/v1/tenants/getTenantsParams" method:"post" tags:"Tenants" summary:"get parameters of tenant"`
	TenantID string `json:"tenant_id" v:"required"`
}

type GetTenantParamsRes struct {
	CodeMessage
	Data *gjson.Json `json:"data"`
}

type RemarkReq struct {
	g.Meta   `path:"/v1/tenants/remark" method:"post" tags:"Tenants" summary:"remark tenant"`
	TenantID string `json:"tenant_id"`
	Remark   string `json:"remark"`
}

type RemarkRes struct {
	CodeMessage
}

type DeactivateTenantReq struct {
	g.Meta   `path:"/v1/tenants/deactivateTenant" method:"post" tags:"Tenants" summary:"deactivate tenant"`
	TenantID string `json:"tenant_id"`
}
type DeactivateTenantRes struct {
	CodeMessage
}
type ActiveTenantReq struct {
	g.Meta   `path:"/v1/tenants/activeTenant" method:"post" tags:"Tenants" summary:"active tenant"`
	TenantID string `json:"tenant_id"`
}
type ActiveTenantRes struct {
	CodeMessage
}

type GetLLMsReq struct {
	g.Meta `path:"/v1/tenants/getLLMs" method:"post" tags:"LLM" summary:"Get all LLMs"`
	Detail bool `json:"detail"`
}
type GetLLMsRes struct {
	g.Meta `mime:"application/json"`
	CodeMessage
	Data []*model.LLMConfiguration `json:"data"`
}
type GetLLMByTenantReq struct {
	g.Meta   `path:"/v1/tenants/getLLMByTenant" method:"post" tags:"LLM" summary:"Get LLM by tenant"`
	TenantID string `json:"tenant_id" v:"required"`
}
type GetLLMByTenantRes struct {
	g.Meta `mime:"application/json"`
	CodeMessage
	Data []*model.LLMConfiguration `json:"data"`
}

type AssignLLMToTenantReq struct {
	g.Meta   `path:"/v1/tenants/assignLLMToTenant" method:"post" tags:"LLM" summary:"Assign LLM to tenant"`
	TenantID string   `json:"tenant_id" v:"required"`
	LLMs     []string `json:"llms" v:"required"`
}
type AssignLLMToTenantRes struct {
	g.Meta `mime:"application/json"`
	CodeMessage
}
type RemoveTenantLLMReq struct {
	g.Meta   `path:"/v1/tenants/removeTenantLLM" method:"post" tags:"LLM" summary:"Remove LLM from tenant"`
	TenantID string   `json:"tenant_id" v:"required"`
	LLMs     []string `json:"llms" v:"required"`
}
type RemoveTenantLLMRes struct {
	g.Meta `mime:"application/json"`
	CodeMessage
}

type GetLLMTenantListReq struct {
	g.Meta `path:"/v1/tenants/getLLMTenantList" method:"post" tags:"LLM" summary:"Get all tenants with LLMs"`
}
type GetLLMTenantListRes struct {
	g.Meta `mime:"application/json"`
	CodeMessage
	Data []*gjson.Json `json:"data"`
}

type NewTokensReq struct {
	g.Meta   `path:"/v1/tenants/gettokens" method:"post" tags:"Tenants" summary:"Get access token and refresh token"`
	TenantID string `json:"tenant_id" v:"required"`
	Password string `json:"password" v:"required"`
	AppKey   string `json:"app_key" v:"required"`
}

type NewTokensRes struct {
	g.Meta       `mime:"application/json"`
	Code         int    `json:"code"`
	Message      string `json:"message"`
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
}

type RefreshTokenReq struct {
	g.Meta       `path:"/v1/tenants/refreshtoken" method:"post" tags:"Tenants" summary:"refresh token"`
	RefreshToken string `json:"refresh_token"`
}

type RefreshTokenRes struct {
	g.Meta      `mime:"application/json"`
	AccessToken string `json:"access_token"`
	CodeMessage
}

type NewTenantReq struct {
	g.Meta   `path:"/v1/tenants/create" method:"post" tags:"Tenants" summary:"create new tenant"`
	TenantID string `json:"tenant_id" v:"required"`
	Password string `json:"password" v:"required"`
}

type NewTenantRes struct {
	g.Meta `mime:"application/json"`
	CodeMessage
}

type ListTenantsReq struct {
	g.Meta `path:"/v1/tenants/list" method:"post" tags:"Tenants" summary:"get all tenants information"`
}

type ListTenantsRes struct {
	g.Meta `mime:"application/json"`
	CodeMessage
}

type UpdateTenantReq struct {
	g.Meta      `path:"/v1/tenants/update" method:"post" tags:"Tenants" summary:"update tenant password"`
	TenantID    string `json:"tenant_id" v:"required"`
	OldPassword string `json:"old_password"`
	NewPassword string `json:"new_password" v:"required" `
}

type UpdateTenantRes struct {
	g.Meta `mime:"application/json"`
	CodeMessage
}

type RemoveTenantReq struct {
	g.Meta   `path:"/v1/tenants/remove" method:"post" tags:"Tenants" summary:"remove tenant"`
	TenantID string `json:"tenant_id" v:"required"`
	Marked   bool   `json:"marked" d:"true" `
}
type RemoveTenantRes struct {
	g.Meta `mime:"application/json"`
	CodeMessage
}

type GenNewKeyReq struct {
	g.Meta   `path:"/v1/tenants/genkey" method:"post" tags:"Tenants" summary:"generate a new app key"`
	TenantID string `json:"tenant_id" v:"required"`
	Password string `json:"password" `
}

type GenNewKeyRes struct {
	g.Meta `mime:"application/json"`
	NewKey string `json:"new_key"`
	CodeMessage
}

type CodeMessage struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

type AuthTenantReq struct {
	g.Meta  `path:"/v1/tenants/auth" method:"post" tags:"Tenants" summary:"authenticate for the specified tenant"`
	Service string `json:"service"`
	Action  string `json:"action"`
}

type AuthTenantRes struct {
	g.Meta `mime:"application/json"`
}

// GetSysInstructionReq 獲取系統指令請求結構體
type GetSysInstructionReq struct {
	g.Meta   `path:"/v1/tenants/getSysInstruction" method:"post" tags:"Tenants" summary:"get system instruction by tenant ID"`
	TenantID string `json:"tenant_id" v:"required"`
}

// GetSysInstructionRes 獲取系統指令響應結構體
type GetSysInstructionRes struct {
	g.Meta `mime:"application/json"`
	CodeMessage
	Data *model.SystemInstruction `json:"data"`
}

// SetSysInstructionReq 設置系統指令請求結構體
type SetSysInstructionReq struct {
	g.Meta            `path:"/v1/tenants/setSysInstruction" method:"post" tags:"Tenants" summary:"set system instruction for tenant"`
	TenantID          string                   `json:"tenant_id" v:"required"`
	SystemInstruction *model.SystemInstruction `json:"system_instruction" v:"required"`
}

// SetSysInstructionRes 設置系統指令響應結構體
type SetSysInstructionRes struct {
	g.Meta `mime:"application/json"`
	CodeMessage
}
