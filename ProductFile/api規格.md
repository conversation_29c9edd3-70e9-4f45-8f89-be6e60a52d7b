# Tenants API 規格文檔

## 概述
本文檔描述了 Tenants 微服務的 API 接口規格，包括租戶管理、認證、LLM 關聯管理以及系統指令管理等功能。

## 基本信息
- **服務名稱**: tenants.svc
- **基礎路徑**: /v1/tenants
- **通訊協議**: HTTP REST API
- **數據格式**: JSON

## 通用響應格式
所有 API 響應都包含以下基本字段：
```json
{
  "code": 0,        // 狀態碼，0 表示成功
  "message": "成功"  // 狀態消息
}
```

## 錯誤碼說明
- `0`: 成功
- `-1`: 系統錯誤
- `1000`: 相同租戶 ID 已存在
- `1001`: 租戶 ID 格式不符合規範
- `1002`: 密碼不符合強密碼規範
- `1003`: 租戶不存在
- `1004`: 密碼錯誤
- `1005`: APP Key 錯誤
- `1006`: 令牌已過期

## API 接口列表

### 租戶管理

#### 1. 創建租戶
- **路徑**: `/v1/tenants/create`
- **方法**: POST
- **描述**: 創建新租戶
- **權限**: 管理員
- **請求參數**:
```json
{
  "tenant_id": "string",  // 租戶ID（必需）
  "password": "string"    // 密碼（必需）
}
```
- **響應**:
```json
{
  "code": 0,
  "message": "成功"
}
```

#### 2. 獲取租戶列表
- **路徑**: `/v1/tenants/list`
- **方法**: POST
- **描述**: 獲取所有租戶信息
- **權限**: 管理員
- **請求參數**: 無
- **響應**:
```json
{
  "code": 0,
  "message": "成功",
  "data": [...]  // 租戶列表
}
```

#### 3. 更新租戶密碼
- **路徑**: `/v1/tenants/update`
- **方法**: POST
- **描述**: 更新租戶密碼
- **請求參數**:
```json
{
  "tenant_id": "string",      // 租戶ID（必需）
  "old_password": "string",   // 舊密碼
  "new_password": "string"    // 新密碼（必需）
}
```

#### 4. 刪除租戶
- **路徑**: `/v1/tenants/remove`
- **方法**: POST
- **描述**: 刪除租戶
- **權限**: 管理員
- **請求參數**:
```json
{
  "tenant_id": "string",  // 租戶ID（必需）
  "marked": true          // 是否標記刪除（默認true）
}
```

#### 5. 檢查或創建租戶
- **路徑**: `/v1/tenants/check`
- **方法**: POST
- **描述**: 檢查租戶是否存在，如果不存在則自動創建並初始化分區表
- **權限**: 無需特殊權限
- **請求參數**:
```json
{
  "tenant_id": "string"  // 租戶ID（必需）
}
```
- **響應**:
```json
{
  "code": 0,
  "message": "成功"
}
```
- **業務邏輯**:
  1. 首先檢查本地緩存中是否存在該租戶
  2. 如果緩存中不存在，檢查 Redis 緩存
  3. 如果 Redis 中不存在，查詢數據庫
  4. 如果數據庫中不存在，創建新租戶（默認密碼為 tenant_id）
  5. 為新創建的租戶自動創建分區表
  6. 將租戶信息緩存到 Redis（過期時間 1 天）

#### 6. 生成新的 APP Key
- **路徑**: `/v1/tenants/genkey`
- **方法**: POST
- **描述**: 為租戶生成新的應用密鑰
- **請求參數**:
```json
{
  "tenant_id": "string",  // 租戶ID（必需）
  "password": "string"    // 密碼
}
```
- **響應**:
```json
{
  "code": 0,
  "message": "成功",
  "new_key": "string"     // 新生成的APP Key
}
```

### 認證相關

#### 6. 獲取訪問令牌
- **路徑**: `/v1/tenants/gettokens`
- **方法**: POST
- **描述**: 獲取訪問令牌和刷新令牌
- **請求參數**:
```json
{
  "tenant_id": "string",  // 租戶ID（必需）
  "password": "string",   // 密碼（必需）
  "app_key": "string"     // APP Key（必需）
}
```
- **響應**:
```json
{
  "code": 0,
  "message": "成功",
  "access_token": "string",   // 訪問令牌
  "refresh_token": "string"   // 刷新令牌
}
```

#### 7. 刷新訪問令牌
- **路徑**: `/v1/tenants/refreshtoken`
- **方法**: POST
- **描述**: 使用刷新令牌獲取新的訪問令牌
- **請求參數**:
```json
{
  "refresh_token": "string"  // 刷新令牌
}
```
- **響應**:
```json
{
  "code": 0,
  "message": "成功",
  "access_token": "string"   // 新的訪問令牌
}
```

#### 8. 租戶權限驗證
- **路徑**: `/v1/tenants/auth`
- **方法**: POST
- **描述**: 驗證租戶對指定服務和操作的權限
- **請求參數**:
```json
{
  "service": "string",  // 服務名稱
  "action": "string"    // 操作名稱
}
```

### 租戶狀態管理

#### 9. 停用租戶
- **路徑**: `/v1/tenants/deactivateTenant`
- **方法**: POST
- **描述**: 停用租戶
- **權限**: 管理員
- **請求參數**:
```json
{
  "tenant_id": "string"  // 租戶ID
}
```

#### 10. 啟用租戶
- **路徑**: `/v1/tenants/activeTenant`
- **方法**: POST
- **描述**: 啟用租戶
- **權限**: 管理員
- **請求參數**:
```json
{
  "tenant_id": "string"  // 租戶ID
}
```

#### 11. 租戶備註
- **路徑**: `/v1/tenants/remark`
- **方法**: POST
- **描述**: 為租戶添加備註
- **權限**: 管理員
- **請求參數**:
```json
{
  "tenant_id": "string",  // 租戶ID
  "remark": "string"      // 備註內容
}
```

### 租戶參數管理

#### 12. 設置租戶參數
- **路徑**: `/v1/tenants/setTenantsParams`
- **方法**: POST
- **描述**: 設置租戶參數
- **權限**: 管理員
- **請求參數**:
```json
{
  "tenant_id": "string",  // 租戶ID（必需）
  "params": {}            // 參數對象
}
```

#### 13. 獲取租戶參數
- **路徑**: `/v1/tenants/getTenantsParams`
- **方法**: POST
- **描述**: 獲取租戶參數
- **權限**: 管理員
- **請求參數**:
```json
{
  "tenant_id": "string"  // 租戶ID（必需）
}
```
- **響應**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {}  // 參數數據
}
```

### 系統指令管理

#### 14. 獲取系統指令
- **路徑**: `/v1/tenants/getSysInstruction`
- **方法**: POST
- **描述**: 獲取指定租戶的系統指令
- **權限**: 管理員
- **請求參數**:
```json
{
  "tenant_id": "string"  // 租戶ID（必需）
}
```
- **響應**:
```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "system": "string",                    // 系統指令
    "service_instructions": [              // 服務指令列表
      {
        "service_id": "string",            // 服務ID
        "channel": "string",               // 通道
        "sys_instruction": "string"        // 系統指令內容
      }
    ]
  }
}
```

#### 15. 設置系統指令
- **路徑**: `/v1/tenants/setSysInstruction`
- **方法**: POST
- **描述**: 設置指定租戶的系統指令
- **權限**: 管理員
- **請求參數**:
```json
{
  "tenant_id": "string",          // 租戶ID（必需）
  "system_instruction": {         // 系統指令對象（必需）
    "system": "string",           // 系統指令
    "service_instructions": [     // 服務指令列表
      {
        "service_id": "string",   // 服務ID
        "channel": "string",      // 通道
        "sys_instruction": "string" // 系統指令內容
      }
    ]
  }
}
```
- **響應**:
```json
{
  "code": 0,
  "message": "成功"
}
```

### LLM 管理

#### 16. 獲取所有 LLM
- **路徑**: `/v1/tenants/getLLMs`
- **方法**: POST
- **描述**: 獲取所有可用的大語言模型
- **權限**: 管理員
- **請求參數**:
```json
{
  "detail": false  // 是否返回詳細信息
}
```

#### 17. 獲取租戶可用的 LLM
- **路徑**: `/v1/tenants/getLLMByTenant`
- **方法**: POST
- **描述**: 獲取指定租戶可用的大語言模型
- **權限**: 管理員
- **請求參數**:
```json
{
  "tenant_id": "string"  // 租戶ID（必需）
}
```

#### 18. 分配 LLM 給租戶
- **路徑**: `/v1/tenants/assignLLMToTenant`
- **方法**: POST
- **描述**: 將大語言模型分配給租戶
- **權限**: 管理員
- **請求參數**:
```json
{
  "tenant_id": "string",    // 租戶ID（必需）
  "llms": ["string"]        // LLM名稱列表（必需）
}
```

#### 19. 移除租戶的 LLM
- **路徑**: `/v1/tenants/removeTenantLLM`
- **方法**: POST
- **描述**: 移除租戶的大語言模型關聯
- **權限**: 管理員
- **請求參數**:
```json
{
  "tenant_id": "string",    // 租戶ID（必需）
  "llms": ["string"]        // LLM名稱列表（必需）
}
```

#### 20. 獲取 LLM-租戶關聯列表
- **路徑**: `/v1/tenants/getLLMTenantList`
- **方法**: POST
- **描述**: 獲取所有 LLM 與租戶的關聯關係
- **權限**: 管理員
- **請求參數**: 無

## 權限說明
- **管理員權限**: 需要在請求頭中提供正確的管理員認證信息
- **租戶權限**: 需要有效的訪問令牌進行身份驗證

## 注意事項
1. 所有需要管理員權限的接口，如果權限驗證失敗將返回 HTTP 401 狀態碼
2. 參數驗證失敗時會返回相應的錯誤碼和錯誤消息
3. 系統指令的設置和獲取涉及敏感配置，僅限管理員操作
4. 建議在生產環境中使用 HTTPS 協議確保數據傳輸安全

## 更新記錄
- 2025-01-15: 新增系統指令管理接口（getSysInstruction, setSysInstruction）
